{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:zombie_pillager", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:attack_behaviors": {"minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 16, "must_see_forget_duration": 0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_visible", "subject": "self", "value": true}]}}, {"filters": {"all_of": [{"test": "is_visible", "subject": "other", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "weakness"}]}, {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "snowgolem"}, {"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}, {"test": "is_family", "subject": "other", "value": "illager"}]}]}, "max_dist": 24}]}, "minecraft:behavior.melee_attack": {"priority": 3, "speed_multiplier": 1.0, "track_target": true}, "minecraft:attack": {"damage": 3}}, "rza:to_pillager": {"minecraft:transformation": {"into": "minecraft:pillager", "begin_transform_sound": "remedy", "transformation_sound": "unfect", "delay": {"value": 15, "block_assist_chance": 0.01, "block_radius": 4, "block_chance": 0.3, "block_types": ["minecraft:bed", "minecraft:iron_bars"]}}, "minecraft:spell_effects": {"add_effects": [{"effect": "weakness", "duration": 180, "amplifier": 0, "visible": true, "ambient": true}, {"effect": "regeneration", "duration": 180, "amplifier": 12, "visible": true, "ambient": true}]}, "minecraft:is_shaking": {}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["rza:attack_behaviors"]}}, "minecraft:entity_transformed": {"add": {"component_groups": ["rza:attack_behaviors"]}}, "rza:cure": {"remove": {"component_groups": ["rza:attack_behaviors"]}, "add": {"component_groups": ["rza:to_pillager"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:nameable": {}, "minecraft:type_family": {"family": ["zombie_illager", "zombie", "undead", "monster", "mob"]}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player?5+(query.equipment_count*math.random(1,3)):0"}, "minecraft:equipment": {"table": "loot_tables/entities/zombies/pillager_gear.lt.json"}, "minecraft:movement": {"value": 0.25}, "minecraft:movement.basic": {}, "minecraft:navigation.walk": {"is_amphibious": true, "can_pass_doors": true, "can_walk": true, "can_float": true, "can_path_over_water": true, "avoid_damage_blocks": false}, "minecraft:jump.static": {}, "minecraft:health": {"value": 15}, "minecraft:behavior.float": {"priority": 5}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}]}}, "deals_damage": "no"}, {"on_damage": {"filters": {"all_of": [{"test": "has_component", "subject": "self", "value": "minecraft:transformation"}, {"test": "has_mob_effect", "subject": "self", "value": "weakness"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "player"}]}}, "deals_damage": "no_but_side_effects_apply"}]}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0, "breathes_air": true, "breathes_water": true}, "minecraft:attack": {"damage": 3}, "minecraft:loot": {"table": "loot_tables/entities/zombie_pillager.json"}, "minecraft:despawn": {"despawn_from_distance": {"max_distance": 64, "min_distance": 32}}, "minecraft:behavior.delayed_attack": {"priority": 3, "speed_multiplier": 1.1, "attack_duration": 0.1, "hit_delay_pct": 0.85, "max_path_time": 1, "random_stop_interval": 120, "x_max_rotation": 30}, "minecraft:behavior.random_stroll": {"priority": 7, "speed_multiplier": 1}, "minecraft:behavior.look_at_player": {"priority": 8, "look_distance": 6, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 9}, "minecraft:interact": {"interactions": {"on_interact": {"filters": {"all_of": [{"test": "has_equipment", "domain": "hand", "subject": "other", "value": "golden_apple"}, {"test": "has_component", "subject": "self", "value": "minecraft:effect.weakness"}]}, "event": "rza:cure", "target": "self"}, "use_item": true, "interact_text": "action.interact.cure"}}, "minecraft:behavior.hurt_by_target": {"priority": 1, "alert_same_type": true, "entity_types": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "operator": "!=", "value": "turret"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "villager"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "illager"}, {"test": "is_visible", "subject": "other", "value": true}]}}}, "minecraft:follow_range": {"value": 64}, "minecraft:dweller": {"dwelling_type": "village", "dweller_role": "hostile", "update_interval_base": 60, "update_interval_variant": 40, "can_find_poi": false, "can_migrate": true, "first_founding_reward": 0}, "minecraft:behavior.move_to_village": {"priority": 4, "speed_multiplier": 1, "goal_radius": 2}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 24, "must_see_forget_duration": 0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_visible", "subject": "other", "value": true}]}}, {"filters": {"all_of": [{"test": "is_visible", "subject": "other", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "weakness"}]}, {"any_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "is_family", "subject": "other", "value": "snowgolem"}, {"test": "is_family", "subject": "other", "value": "irongolem"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}, {"test": "is_family", "subject": "other", "value": "illager"}]}]}, "max_dist": 24}]}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:entity_sensor": {"subsensors": [{"range": [4.0, 4.0], "event_filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "villager"}, {"test": "has_mob_effect", "subject": "self", "value": "weakness"}, {"test": "has_component", "subject": "self", "operator": "!=", "value": "minecraft:transformation"}]}, "event": "rza:cure"}]}, "minecraft:conditional_bandwidth_optimization": {}}}}