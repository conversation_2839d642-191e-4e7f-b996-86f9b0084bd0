/**
 * @file Sonic Cannon functionality for the Zombie Apocalypse addon.
 * Handles sonic blast impulse mechanics for both regular and attachment variants.
 */

import { Entity, EntityDamageCause, Vector3 } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";

/**
 * Converts entity rotation (yaw and pitch) to a normalized direction vector
 * @param rotation The entity's rotation with yaw (y) and pitch (x) in degrees
 * @returns A normalized Vector3 representing the direction
 */
function rotationToDirection(rotation: { x: number; y: number }): Vector3 {
  // Convert degrees to radians
  const yawRad = (rotation.y * Math.PI) / 180;
  const pitchRad = (rotation.x * Math.PI) / 180;

  // Calculate direction vector from rotation
  // x = -sin(yaw) * cos(pitch)
  // y = -sin(pitch)
  // z = cos(yaw) * cos(pitch)
  const direction: Vector3 = { x: -Math.sin(yawRad) * Math.cos(pitchRad), y: -Math.sin(pitchRad), z: Math.cos(yawRad) * Math.cos(pitchRad) };

  return direction;
}

/**
 * Applies a sonic blast impulse to the target entity from a regular sonic cannon.
 * The blast creates a strong upward force combined with horizontal movement.
 *
 * @param {Entity} entity - The target entity to receive the sonic blast
 * @param {Entity} source - The entity firing the sonic cannon (used for direction)
 * @returns {void}
 */
export function sonicCannonHit(entity: Entity, source: Entity): void {
  const cannonDir = source.getViewDirection();
  entity.applyImpulse({
    x: cannonDir.x * 3, // Horizontal force multiplier
    y: cannonDir.y * 8, // Strong vertical force
    z: cannonDir.z * 3 // Horizontal force multiplier
  });
  return;
}

/**
 * Fires a sonic charge blast using raycast based on rotation
 * @param entity The entity firing the sonic charge
 */
export function fireSonicCharge(entity: Entity): void {
  const dimension = entity.dimension;
  const location = entity.location;
  const rotation = entity.getRotation();

  // Calculate direction from cannon rotation
  const direction = rotationToDirection(rotation);

  const startOffset = 1.5;
  const startPos: Vector3 = {
    x: location.x + direction.x * startOffset,
    y: location.y + 0.55 + direction.y * startOffset,
    z: location.z + direction.z * startOffset
  };

  // Get ray positions with fixed length raycast (32 blocks)
  const positions = fixedLenRaycast(startPos, direction, 48, 2);

  // Handle particles and damage for each position
  for (const pos of positions) {
    try {
      dimension.spawnParticle("rza:sonic_charge", pos);
      dimension.getEntities({ location: pos, families: ["zombie"], maxDistance: 5 }).forEach((zombie) => {
        zombie.applyDamage(10, { cause: EntityDamageCause.entityAttack, damagingEntity: entity });
      });
    } catch (e) {}
  }
  return;
}
