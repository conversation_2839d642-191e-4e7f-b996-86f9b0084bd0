import { world, system } from "@minecraft/server";
import { itemFeatures } from "./items";
class RzaItemComponent {
    onUse(event) {
        const item = event.itemStack;
        const player = event.source;
        if (!item)
            return;
        const itemTypeId = item.type.id;
        itemFeatures(item, itemTypeId, player);
    }
}
system.beforeEvents.startup.subscribe((initEvent) => {
    initEvent.itemComponentRegistry.registerCustomComponent("rza:item", new RzaItemComponent());
});
world.beforeEvents.itemUse.subscribe((data) => {
    const item = data.itemStack;
    const player = data.source;
    if (!item)
        return;
    itemFeatures(item, item.typeId, player, true);
});
world.afterEvents.itemStopUse.subscribe((data) => {
    const item = data.itemStack;
    const player = data.source;
    if (!item)
        return;
    itemFeatures(item, item.typeId, player, false);
});
