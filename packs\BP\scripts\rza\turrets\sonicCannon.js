import { EntityDamageCause } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";
function rotationToDirection(rotation) {
    const yawRad = (rotation.y * Math.PI) / 180;
    const pitchRad = (rotation.x * Math.PI) / 180;
    const direction = { x: -Math.sin(yawRad) * Math.cos(pitchRad), y: -Math.sin(pitchRad), z: Math.cos(yawRad) * Math.cos(pitchRad) };
    return direction;
}
export function sonicCannonHit(entity, source) {
    const cannonDir = source.getViewDirection();
    entity.applyImpulse({
        x: cannonDir.x * 3,
        y: cannonDir.y * 8,
        z: cannonDir.z * 3
    });
    return;
}
export function fireSonicCharge(entity) {
    const dimension = entity.dimension;
    const location = entity.location;
    const rotation = entity.getRotation();
    const direction = rotationToDirection(rotation);
    const startOffset = 1.5;
    const startPos = {
        x: location.x + direction.x * startOffset,
        y: location.y + 0.55 + direction.y * startOffset,
        z: location.z + direction.z * startOffset
    };
    const positions = fixedLenRaycast(startPos, direction, 48, 2);
    for (const pos of positions) {
        try {
            dimension.spawnParticle("rza:sonic_charge", pos);
            dimension.getEntities({ location: pos, families: ["zombie"], maxDistance: 5 }).forEach((zombie) => {
                zombie.applyDamage(10, { cause: EntityDamageCause.entityAttack, damagingEntity: entity });
            });
        }
        catch (e) { }
    }
    return;
}
