{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "rza:miner", "materials": {"default": "rab_zombie", "face": "zombie_face"}, "textures": {"default": "textures/entity/miner/miner", "face1_regular": "textures/entity/zombie_faces/regular/face1", "face2_regular": "textures/entity/zombie_faces/regular/face2", "face3_regular": "textures/entity/zombie_faces/regular/face3", "face4_regular": "textures/entity/zombie_faces/regular/face4", "face5_regular": "textures/entity/zombie_faces/regular/face5", "face1_mutated": "textures/entity/zombie_faces/mutated/face1", "face2_mutated": "textures/entity/zombie_faces/mutated/face2", "face3_mutated": "textures/entity/zombie_faces/mutated/face3", "face4_mutated": "textures/entity/zombie_faces/mutated/face4", "face5_mutated": "textures/entity/zombie_faces/mutated/face5", "torso_wound1": "textures/entity/zombie_wounds/torso_wound1", "torso_wound2": "textures/entity/zombie_wounds/torso_wound2", "torso_wound3": "textures/entity/zombie_wounds/torso_wound3", "torso_wound4": "textures/entity/zombie_wounds/torso_wound4", "torso_wound5": "textures/entity/zombie_wounds/torso_wound5", "limb_wound1": "textures/entity/zombie_wounds/limb_wound1", "limb_wound2": "textures/entity/zombie_wounds/limb_wound2", "limb_wound3": "textures/entity/zombie_wounds/limb_wound3", "limb_wound4": "textures/entity/zombie_wounds/limb_wound4", "limb_wound5": "textures/entity/zombie_wounds/limb_wound5", "limb_wound6": "textures/entity/zombie_wounds/limb_wound6", "limb_wound7": "textures/entity/zombie_wounds/limb_wound7"}, "geometry": {"default": "geometry.walker.miner", "death": "geometry.walker.miner_death"}, "animations": {"look_at_target": "animation.common.look_at_target", "idle1": "animation.walker.normal.idle1", "idle2": "animation.walker.normal.idle2", "idle3": "animation.walker.normal.idle3", "idle4": "animation.walker.normal.idle4", "idle5": "animation.walker.normal.idle5", "walk1": "animation.walker.normal.walk1", "walk2": "animation.walker.normal.walk2", "walk3": "animation.walker.normal.walk3", "walk4": "animation.walker.normal.walk4", "walk5": "animation.walker.normal.walk5", "attack1": "animation.walker.normal.attack1", "attack2": "animation.walker.normal.attack2", "attack3": "animation.walker.normal.attack3", "death1": "animation.walker.normal.death1", "death2": "animation.walker.normal.death2", "death3": "animation.walker.normal.death3", "death4": "animation.walker.normal.death4", "death_rot": "animation.general.death_rot", "general": "controller.animation.walker.normal.general", "attack_controller": "controller.animation.walker.normal.attack", "death_controller": "controller.animation.walker.normal.death"}, "scripts": {"initialize": ["variable.faces = math.random_integer(0, 30);", "variable.torso_wounds =  math.random_integer(0, 30);", "variable.limb_wounds = math.random_integer(0, 30);"], "animate": ["death_controller"]}, "particle_effects": {"blood": "rza:blood"}, "render_controllers": ["controller.render.miner.base", "controller.render.zombie.faces", "controller.render.zombie.torso_wounds", "controller.render.zombie.limb_wounds"], "spawn_egg": {"base_color": "#112114", "overlay_color": "#C16412"}}}}