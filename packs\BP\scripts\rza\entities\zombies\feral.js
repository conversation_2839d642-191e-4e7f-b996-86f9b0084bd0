import { EntityComponentTypes } from "@minecraft/server";
export function ferralLeap(entity) {
    const feral = entity;
    const jumpPowerMin = 1;
    const jumpPowerMax = 2;
    const jumpPowerVal = Math.random() * (jumpPowerMax - jumpPowerMin) + jumpPowerMin;
    const jumpPower = Number(jumpPowerVal.toFixed(3));
    const viewDirection = feral.getViewDirection();
    const targets = feral.dimension.getEntitiesFromRay(feral.getHeadLocation(), viewDirection, { maxDistance: 2 });
    for (const entity of targets) {
        const target = entity.entity;
        const excludedEntity = target.hasComponent(EntityComponentTypes.TypeFamily) &&
            target.getComponent(EntityComponentTypes.TypeFamily)
                .getTypeFamilies()
                .some((family) => family == "zombie" || family == "turret" || family == "inanimate");
        if (!excludedEntity) {
            try {
                const direction = { x: viewDirection.x, z: viewDirection.z };
                target.applyKnockback(direction, 1);
            }
            catch (error) { }
        }
    }
    feral.applyImpulse({ x: viewDirection.x * jumpPower, y: viewDirection.y * jumpPower, z: viewDirection.z * jumpPower });
}
