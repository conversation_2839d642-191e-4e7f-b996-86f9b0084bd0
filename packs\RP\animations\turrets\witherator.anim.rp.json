{"format_version": "1.8.0", "animations": {"animation.witherator.fire": {"loop": "hold_on_last_frame", "animation_length": 2.25, "bones": {"launcher1": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher2": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher3": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher4": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher5": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher6": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher7": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "launcher8": {"position": {"0.0": [0, 0, 0], "0.0417": [0, -0.58605, 0], "0.0833": [0, -0.80018, 0], "0.125": [0, -0.93901, 0], "0.1667": [0, -1.03864, 0], "0.2083": [0, -1.11306, 0], "0.25": [0, -1.16962, 0], "0.2917": [0, -1.21256, 0], "0.5417": [0, -1.29693, 0], "1.0833": [0, -1.00958, 0], "1.625": [0, -0.44185, 0], "1.9583": [0, -0.03554, 0], "2.0": [0, 0, 0]}}, "platform": {"position": {"0.0": [0, 0, 0], "0.1667": [0, -0.58605, 0], "0.3333": [0, -0.80018, 0], "0.375": [0, -0.93901, 0], "0.4167": [0, -1.03864, 0], "0.4583": [0, -1.11306, 0], "0.5": [0, -1.16962, 0], "0.5417": [0, -1.21256, 0], "0.7917": [0, -1.29693, 0], "1.3333": [0, -1.00958, 0], "1.875": [0, -0.44185, 0], "2.2083": [0, -0.03554, 0], "2.25": [0, 0, 0]}}}, "particle_effects": {"0.0": [{"effect": "shoot_exhaust", "locator": "launcher1"}, {"effect": "shoot_exhaust", "locator": "launcher2"}, {"effect": "shoot_exhaust", "locator": "launcher3"}, {"effect": "shoot_exhaust", "locator": "launcher4"}, {"effect": "shoot_exhaust", "locator": "launcher5"}, {"effect": "shoot_exhaust", "locator": "launcher6"}, {"effect": "shoot_exhaust", "locator": "launcher7"}, {"effect": "shoot_exhaust", "locator": "launcher8"}], "0.4167": [{"effect": "post_exhaust", "locator": "launcher1"}, {"effect": "post_exhaust", "locator": "launcher2"}, {"effect": "post_exhaust", "locator": "launcher3"}, {"effect": "post_exhaust", "locator": "launcher4"}, {"effect": "post_exhaust", "locator": "launcher5"}, {"effect": "post_exhaust", "locator": "launcher6"}, {"effect": "post_exhaust", "locator": "launcher7"}, {"effect": "post_exhaust", "locator": "launcher8"}]}}, "animation.witherator.scale": {"loop": true, "bones": {"platform": {"position": [0, -0.25, 0], "scale": 1.3}}}}}