{"format_version": "1.10.0", "render_controllers": {"controller.render.zombie.faces": {"arrays": {"textures": {"Array.faces_regular": ["Texture.face1_regular", "Texture.face2_regular", "Texture.face3_regular", "Texture.face4_regular", "Texture.face5_regular"], "Array.faces_mutated": ["Texture.face1_mutated", "Texture.face2_mutated", "Texture.face3_mutated", "Texture.face4_mutated", "Texture.face5_mutated"]}, "geometries": {"array.geos": ["Geometry.default", "Geometry.death"]}}, "geometry": "q.is_alive?geometry.default:geometry.death", "materials": [{"*": "material.face"}], "textures": ["q.property('rza:mutated') == false ? array.faces_regular[variable.faces] : array.faces_mutated[variable.faces]"], "part_visibility": [{"*": false}, {"head": true}, {"jaw": true}], "is_hurt_color": {"r": 1, "g": 0, "b": 0, "a": "q.is_alive?0.5:0"}}, "controller.render.zombie.torso_wounds": {"arrays": {"textures": {"Array.torso_wounds": ["Texture.torso_wound1", "Texture.torso_wound2", "Texture.torso_wound3", "Texture.torso_wound4", "Texture.torso_wound5"]}, "geometries": {"array.geos": ["Geometry.default", "Geometry.death"]}}, "geometry": "q.is_alive?geometry.default:geometry.death", "materials": [{"*": "material.default"}], "textures": ["array.torso_wounds[variable.torso_wounds]"], "part_visibility": [{"*": true}, {"head": false}], "is_hurt_color": {"r": 1, "g": 0, "b": 0, "a": "q.is_alive?0.5:0"}}, "controller.render.zombie.limb_wounds": {"arrays": {"textures": {"Array.limb_wounds": ["Texture.limb_wound1", "Texture.limb_wound2", "Texture.limb_wound3", "Texture.limb_wound4", "Texture.limb_wound5", "Texture.limb_wound6", "Texture.limb_wound7"]}, "geometries": {"array.geos": ["Geometry.default", "Geometry.death"]}}, "geometry": "q.is_alive?geometry.default:geometry.death", "materials": [{"*": "material.default"}], "textures": ["array.limb_wounds[variable.limb_wounds]"], "part_visibility": [{"*": true}, {"head": false}], "is_hurt_color": {"r": 1, "g": 0, "b": 0, "a": "q.is_alive?0.5:0"}}}}