import { system } from "@minecraft/server";
import { blockFeatures } from "./blocks";
class RzaTickComponent {
    onTick(event) {
        const block = event.block;
        const blockTypeId = block.type.id;
        blockFeatures(block, blockTypeId);
    }
}
system.beforeEvents.startup.subscribe((initEvent) => {
    initEvent.blockComponentRegistry.registerCustomComponent("rza:tick", new RzaTickComponent());
});
