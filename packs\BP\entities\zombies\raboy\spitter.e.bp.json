{
  "format_version": "1.21.90",
  "minecraft:entity": {
    "description": {
      "identifier": "rza:spitter",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false,
      "properties": { "rza:mutated": { "client_sync": true, "type": "bool", "default": false } }
    },
    "component_groups": {
      "rza:zombie_idle": {
        "minecraft:on_target_acquired": {
          "filters": {
            "all_of": [
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "turret" },
              { "test": "is_visible", "subject": "other", "value": true }
            ]
          },
          "event": "minecraft:become_angry",
          "target": "self"
        }
      },
      "rza:zombie_target_acquired": {
        "minecraft:angry": {
          "broadcast_anger": true,
          "broadcast_range": 48,
          "broadcast_targets": ["zombie"],
          "duration": 8,
          "broadcast_filters": {
            "all_of": [
              { "test": "has_target", "subject": "other", "operator": "!=", "value": false },
              { "none_of": [{ "test": "has_mob_effect", "subject": "other", "value": "weakness" }] }
            ]
          },
          "filters": {
            "all_of": [
              { "test": "is_visible", "subject": "other", "value": true },
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "zombie" }
            ]
          },
          "calm_event": { "event": "minecraft:on_calm", "target": "self" }
        }
      },
      "rza:spitter_regular": {
        "minecraft:shooter": { "def": "rza:spitter_acid_normal" },
        "minecraft:behavior.ranged_attack": {
          "speed_multiplier": 1,
          "attack_radius_min": 8,
          "attack_interval": 5,
          "attack_radius": 12,
          "swing": true
        },
        "minecraft:equipment": { "table": "loot_tables/equipment/spitter/regular.lt.json" },
        "minecraft:health": { "value": 10 },
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(1,12)):0" }
      },
      "rza:spitter_mutated": {
        "minecraft:shooter": { "def": "rza:spitter_acid_mutated" },
        "minecraft:behavior.ranged_attack": {
          "speed_multiplier": 1.6,
          "attack_radius_min": 8,
          "attack_interval": 5,
          "attack_radius": 24,
          "swing": true
        },
        "minecraft:equipment": { "table": "loot_tables/equipment/spitter/tnt_thrower.lt.json" },
        "minecraft:health": { "value": 20 },
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(5,20)):0" }
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "sequence": [
          { "queue_command": { "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate" } },
          { "add": { "component_groups": ["rza:spitter_regular", "rza:zombie_idle"] } }
        ]
      },
      "rza:mutate": {
        "sequence": [
          //When not on full moon
          {
            "filters": { "test": "moon_phase", "operator": "!=", "value": 0 },
            "randomize": [
              { "weight": 100 },
              {
                "weight": 3,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "easy" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              },
              {
                "weight": 8,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "normal" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              },
              {
                "weight": 12,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "hard" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              }
            ]
          },
          //During full moon
          {
            "filters": { "test": "moon_phase", "operator": "==", "value": 0 },
            "randomize": [
              { "weight": 100 },
              {
                "weight": 15,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "easy" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              },
              {
                "weight": 24,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "normal" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              },
              {
                "weight": 45,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "hard" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:spitter_regular"] } },
                  { "add": { "component_groups": ["rza:spitter_mutated"] } }
                ]
              }
            ]
          }
        ]
      },
      "minecraft:become_angry": {
        "remove": { "component_groups": ["rza:zombie_idle"] },
        "add": { "component_groups": ["rza:zombie_target_acquired"] }
      },
      "minecraft:on_calm": { "remove": { "component_groups": ["rza:zombie_target_acquired"] }, "add": { "component_groups": ["rza:zombie_idle"] } }
    },
    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:nameable": {},
      "minecraft:type_family": { "family": ["zombie", "spitter", "undead", "monster", "mob"] },
      "minecraft:equip_item": {},
      "minecraft:collision_box": { "width": 0.7, "height": 2.4 },
      "minecraft:movement": { "value": 0.25 },
      "minecraft:movement.basic": {},
      "minecraft:navigation.walk": {
        "is_amphibious": true,
        "can_pass_doors": true,
        "can_walk": true,
        "can_float": true,
        "can_path_over_water": true,
        "avoid_damage_blocks": false
      },
      "minecraft:jump.static": {},
      "minecraft:can_climb": {},
      "minecraft:behavior.float": { "priority": 5 },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          { "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true }, "cause": "lava", "damage_per_tick": 4 }
        ]
      },
      "minecraft:breathable": { "total_supply": 15, "suffocate_time": 0, "breathes_air": true, "breathes_water": true },
      "minecraft:loot": { "table": "loot_tables/entities/zombies/spitter.lt.json" },
      "minecraft:despawn": { "despawn_from_distance": { "max_distance": 128, "min_distance": 32 } },
      "minecraft:behavior.equip_item": { "priority": 2 },
      "minecraft:behavior.pickup_items": {
        "priority": 6,
        "max_dist": 3,
        "goal_radius": 2,
        "speed_multiplier": 1,
        "pickup_based_on_chance": true,
        "can_pickup_any_item": true,
        "excluded_items": ["minecraft:glow_ink_sac"]
      },
      "minecraft:behavior.random_stroll": { "priority": 7, "speed_multiplier": 1 },
      "minecraft:behavior.look_at_player": { "priority": 8, "look_distance": 6, "probability": 0.02 },
      "minecraft:behavior.random_look_around": { "priority": 9 },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "alert_same_type": true,
        "entity_types": {
          "filters": {
            "all_of": [
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "turret" },
              { "test": "is_visible", "subject": "other", "value": true }
            ]
          }
        }
      },
      "minecraft:follow_range": { "value": 64 },
      "minecraft:dweller": {
        "dwelling_type": "village",
        "dweller_role": "hostile",
        "update_interval_base": 60,
        "update_interval_variant": 40,
        "can_find_poi": false,
        "can_migrate": true,
        "first_founding_reward": 0
      },
      "minecraft:behavior.move_to_village": { "priority": 4, "speed_multiplier": 1, "goal_radius": 2 },
      "minecraft:damage_sensor": {
        "triggers": [
          { "on_damage": { "filters": { "any_of": [{ "test": "is_family", "subject": "other", "value": "zombie" }] } }, "deals_damage": "no" }
        ]
      },
      "minecraft:behavior.nearest_prioritized_attackable_target": {
        "priority": 2,
        "must_see": true,
        "reselect_targets": true,
        "within_radius": 24,
        "must_see_forget_duration": 0,
        "entity_types": [
          {
            "priority": 0,
            "filters": {
              "all_of": [
                { "test": "is_visible", "subject": "other", "value": true },
                {
                  "any_of": [
                    { "test": "is_family", "subject": "other", "value": "player" },
                    { "test": "is_family", "subject": "other", "value": "snowgolem" },
                    { "test": "is_family", "subject": "other", "value": "irongolem" },
                    { "test": "is_family", "subject": "other", "value": "villager" },
                    { "test": "is_family", "subject": "other", "value": "wandering_trader" },
                    { "test": "is_family", "subject": "other", "value": "illager" }
                  ]
                }
              ]
            },
            "max_dist": 24
          },
          {
            "priority": 1,
            "filters": {
              "all_of": [
                { "test": "is_visible", "subject": "other", "value": true },
                { "any_of": [{ "test": "is_family", "subject": "other", "value": "turret" }] }
              ]
            },
            "max_dist": 24
          }
        ]
      },
      "minecraft:physics": {},
      "minecraft:pushable": { "is_pushable": true, "is_pushable_by_piston": true },
      "minecraft:conditional_bandwidth_optimization": {},
      "minecraft:ambient_sound_interval": { "event_name": "ambient", "range": 10, "value": 7 }
    }
  }
}
