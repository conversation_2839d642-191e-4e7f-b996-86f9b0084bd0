{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "rza:spitter_acid_explode_normal", "basic_render_parameters": {"material": "particles_add", "texture": "textures/particle/zombies/acid"}}, "components": {"minecraft:emitter_rate_instant": {"num_particles": "math.random_integer(32, 64)"}, "minecraft:emitter_lifetime_once": {"active_time": 5}, "minecraft:emitter_shape_point": {"direction": ["math.random_integer(-8, 8)", 32, "math.random_integer(-8, 8)"]}, "minecraft:particle_initialization": {"per_update_expression": "variable.stuck_time=variable.particle_random_1*4+1;", "per_render_expression": "variable.stuck_time=variable.particle_random_1*4+1;"}, "minecraft:particle_lifetime_expression": {"max_lifetime": "math.random(5, 10)"}, "minecraft:particle_initial_spin": {"rotation": "math.random(0,45)"}, "minecraft:particle_initial_speed": "math.random(4, 16)", "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, -20, 0], "linear_drag_coefficient": 0.3}, "minecraft:particle_appearance_billboard": {"size": ["v.particle_age<3?0.05+v.particle_age*0.08:0.3", "v.particle_age<3?0.05+v.particle_age*0.08:0.3"], "facing_camera_mode": "direction_y", "uv": {"texture_width": 16, "texture_height": 16, "uv": [0, 0], "uv_size": [16, 16]}}, "minecraft:particle_motion_collision": {"enabled": "variable.particle_age>=0.1", "collision_drag": 8, "collision_radius": 0.05}, "minecraft:particle_appearance_tinting": {"color": {"interpolant": "v.particle_age / v.particle_lifetime", "gradient": {"0.0": "#FF0D8C0F", "0.5": "#FF104F19", "1.0": "#FF30F378"}}}}}}