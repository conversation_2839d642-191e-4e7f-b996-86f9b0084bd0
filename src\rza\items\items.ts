import { ItemStack, Player, system } from "@minecraft/server";
import { handleFlamethrower } from "../weapons/flamethrower";

/**
 * This function runs whenever an item is used
 */
export function itemFeatures(itemStack: ItemStack, itemTypeId: string, player: Player, startUsing?: boolean) {
  if (itemTypeId === "rza:flamethrower") {
    let fire = system.run(() => {
      handleFlamethrower(itemStack, player, startUsing);
      system.clearRun(fire);
    });
  }
  return;
}
