import {
  Entity,
  EntityComponentTypes,
  EntityDamageCause,
  EntityEquippableComponent,
  EquipmentSlot,
  ItemComponentTypes,
  ItemEnchantableComponent,
  ItemStack,
  system,
  VectorXZ
} from "@minecraft/server";
import { createShockwave } from "../entities/general/general";

// Map to store cooldown for each wielder
export const meleeWeaponCooldown = new Map();

// Function for player melee weapon attack
export function player<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nAttack(entityHit: Entity, wielder: Entity, weapon: ItemStack, damage?: number) {
  const weaponId = weapon?.type.id;
  const hitLocation = entityHit.location;
  meleeWeaponCooldown.set(wielder.id, 20);

  if (weaponId?.endsWith("axe") || weaponId?.endsWith("sword")) {
    // Spawn particle effect to indicate the melee attack
    entityHit.dimension.spawnParticle("rza:melee_sweep", { x: hitLocation.x, y: hitLocation.y + 0.5, z: hitLocation.z });

    // Play sound effect for the melee attack
    entityHit.dimension.playSound("weapon.melee.sweep", { x: hitLocation.x, y: hitLocation.y + 0.5, z: hitLocation.z }, { volume: 2 });

    // Check if the weapon has the sharpness enchantment
    const equippableComponent = wielder.getComponent(EntityComponentTypes.Equippable) as EntityEquippableComponent;
    const mainhandEquipment = equippableComponent?.getEquipment(EquipmentSlot.Mainhand);
    const enchantableComponent = mainhandEquipment?.getComponent(ItemComponentTypes.Enchantable) as ItemEnchantableComponent;
    const hasSharpnessEnchantment = enchantableComponent?.hasEnchantment("sharpness");
    const sharpnessLevel = enchantableComponent?.getEnchantment("sharpness")?.level;

    // Get nearby entities within a certain range
    const nearbyEntities = entityHit.dimension.getEntities({
      location: hitLocation,
      minDistance: 1,
      maxDistance: 3,
      excludeFamilies: ["player", "turret", "inanimate", "utility", "illager", "villager", "irongolem", "wandering_trader"]
    });

    // Apply damage to each nearby entity
    for (const hit of nearbyEntities) {
      const damage = hasSharpnessEnchantment ? 0 + (sharpnessLevel ?? 0) : 1;
      hit.applyDamage(damage, { damagingEntity: wielder, cause: EntityDamageCause.entityAttack });
    }
  } else if (weaponId?.endsWith("mace")) {
    if (damage && damage >= 15) {
      // Calculate dynamic max force based on damage
      // Clamp damage between 15 and 40, then map it to a more moderate force range 0.5-1.5
      const clampedDamage = Math.min(Math.max(damage, 15), 100);
      const maxForce = 0.5 + ((clampedDamage - 15) / (100 - 15)) * (2.5 - 0.5);
      createShockwave(wielder, 5, maxForce, 0.1, 3, ["zombie"]);
    } else if (damage && damage < 15) {
      // Calculate forward direction based on wielder's position
      const direction: VectorXZ = { x: entityHit.location.x - wielder.location.x, z: entityHit.location.z - wielder.location.z };

      // Normalize the direction
      const magnitude = Math.sqrt(direction.x * direction.x + direction.z * direction.z);

      try {
        // Apply a standard forward knockback with slight upward force
        entityHit.applyImpulse({ x: (direction.x / magnitude) * 0.75, y: 0.3, z: (direction.z / magnitude) * 0.75 });
      } catch (error) {
        try {
          // Fallback to basic knockback if impulse fails
          entityHit.applyKnockback(direction, 0.75);
        } catch (fallbackError) {
          console.warn(`Failed to apply knockback to entity ${entityHit.typeId}: ${fallbackError}`);
        }
      }
    }
  }

  // Run interval to update weapon cooldown
  const cooldown = system.runInterval(() => {
    const cooldownTime = meleeWeaponCooldown.get(wielder.id) as number;
    meleeWeaponCooldown.set(wielder.id, Math.max(cooldownTime - 1, 0));
    if (cooldownTime === 0) {
      system.clearRun(cooldown);
    }
  });
  return;
}

// Function for non-player melee weapon attack
export function nonPlayerMeleeWeaponAttack(entityHit: Entity, wielder: Entity) {
  meleeWeaponCooldown.set(wielder.id, 20);
  const hitLocation = entityHit.location;

  // Spawn particle effect to indicate the melee attack
  entityHit.dimension.spawnParticle("rza:melee_sweep", { x: hitLocation.x, y: hitLocation.y + 0.5, z: hitLocation.z });

  // Play sound effect for the melee attack
  entityHit.dimension.playSound("weapon.melee.sweep", { x: hitLocation.x, y: hitLocation.y + 0.5, z: hitLocation.z }, { volume: 2 });

  // Get nearby entities within a certain range
  const nearbyEntities = entityHit.dimension.getEntities({
    location: hitLocation,
    minDistance: 1,
    maxDistance: 2.5,
    excludeFamilies: ["player", "turret", "inanimate", "utility", "illager", "villager", "irongolem", "wandering_trader"]
  });

  // Apply damage to each nearby entity
  for (const hit of nearbyEntities) {
    hit.applyDamage(2, { damagingEntity: wielder, cause: EntityDamageCause.entityAttack });
  }

  // Run interval to update weapon cooldown
  const cooldown = system.runInterval(() => {
    const cooldownTime = meleeWeaponCooldown.get(wielder.id);
    meleeWeaponCooldown.set(wielder.id, Math.max(cooldownTime - 1, 0));
    if (cooldownTime === 0) {
      system.clearRun(cooldown);
    }
  });
  return;
}
