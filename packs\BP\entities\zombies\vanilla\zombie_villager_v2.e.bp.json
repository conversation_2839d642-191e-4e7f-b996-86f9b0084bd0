{
  "format_version": "1.21.90",
  "minecraft:entity": {
    "description": { "identifier": "minecraft:zombie_villager_v2", "is_spawnable": true, "is_summonable": false, "is_experimental": false },
    "component_groups": {
      "baby": {
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?12+(query.equipment_count*math.random(1,3)):0" },
        "minecraft:is_baby": {},
        "minecraft:scale": { "value": 0.5 },
        "minecraft:movement": { "value": 0.35 }
      },
      "adult": {
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?5+(query.equipment_count*math.random(1,3)):0" },
        "minecraft:movement": { "value": 0.23 },
        "minecraft:rideable": { "seat_count": 1, "family_types": ["zombie"], "seats": { "position": [0, 1.1, -0.35] } },
        "minecraft:behavior.mount_pathing": { "priority": 5, "speed_multiplier": 1.25, "target_dist": 0, "track_target": true }
      },
      "jockey": { "minecraft:behavior.find_mount": { "priority": 1, "within_radius": 16 } },
      "can_break_doors": { "minecraft:annotation.break_door": {} },
      "from_abandoned_village": {
        "minecraft:navigation.walk": {
          "is_amphibious": true,
          "can_pass_doors": true,
          "can_open_doors": true,
          "avoid_water": true,
          "avoid_sun": true
        },
        "minecraft:behavior.flee_sun": { "priority": 4, "speed_multiplier": 1 }
      },
      "to_villager": {
        "minecraft:transformation": {
          "into": "minecraft:villager_v2",
          "begin_transform_sound": "remedy",
          "transformation_sound": "unfect",
          "drop_equipment": true,
          "keep_level": true,
          "delay": {
            "value": 15,
            "block_assist_chance": 0.01,
            "block_radius": 4,
            "block_chance": 0.3,
            "block_types": ["minecraft:bed", "minecraft:iron_bars"]
          }
        },
        "minecraft:spell_effects": {
          "add_effects": [
            { "effect": "weakness", "duration": 180, "amplifier": 0, "visible": true, "ambient": true },
            { "effect": "regeneration", "duration": 180, "amplifier": 12, "visible": true, "ambient": true }
          ]
        },
        "minecraft:is_shaking": {}
      },
      "unskilled": {
        "minecraft:type_family": { "family": ["unskilled", "zombie", "zombie_villager", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 0 }
      },
      "farmer": {
        "minecraft:type_family": { "family": ["farmer", "zombie", "zombie_villager", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 1 }
      },
      "fisherman": {
        "minecraft:type_family": { "family": ["fisherman", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 2 }
      },
      "shepherd": {
        "minecraft:type_family": { "family": ["shepherd", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 3 }
      },
      "fletcher": {
        "minecraft:type_family": { "family": ["fletcher", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 4 }
      },
      "librarian": {
        "minecraft:type_family": { "family": ["librarian", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 5 }
      },
      "cartographer": {
        "minecraft:type_family": { "family": ["cartographer", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 6 }
      },
      "cleric": {
        "minecraft:type_family": { "family": ["cleric", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 7 }
      },
      "armorer": {
        "minecraft:type_family": { "family": ["armorer", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 8 }
      },
      "weaponsmith": {
        "minecraft:type_family": { "family": ["weaponsmith", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 9 }
      },
      "toolsmith": {
        "minecraft:type_family": { "family": ["toolsmith", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 10 }
      },
      "butcher": {
        "minecraft:type_family": { "family": ["butcher", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 11 }
      },
      "leatherworker": {
        "minecraft:type_family": { "family": ["leatherworker", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 12 }
      },
      "mason": {
        "minecraft:type_family": { "family": ["stone_mason", "zombie_villager", "zombie", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 13 }
      },
      "nitwit": {
        "minecraft:type_family": { "family": ["nitwit", "zombie", "zombie_villager", "undead", "monster", "mob"] },
        "minecraft:variant": { "value": 14 }
      },
      "desert_villager": { "minecraft:mark_variant": { "value": 1 } },
      "jungle_villager": { "minecraft:mark_variant": { "value": 2 } },
      "savanna_villager": { "minecraft:mark_variant": { "value": 3 } },
      "snow_villager": { "minecraft:mark_variant": { "value": 4 } },
      "swamp_villager": { "minecraft:mark_variant": { "value": 5 } },
      "taiga_villager": { "minecraft:mark_variant": { "value": 6 } },
      "villager_skin_0": { "minecraft:skin_id": { "value": 0 } },
      "villager_skin_1": { "minecraft:skin_id": { "value": 1 } },
      "villager_skin_2": { "minecraft:skin_id": { "value": 2 } },
      "villager_skin_3": { "minecraft:skin_id": { "value": 3 } },
      "villager_skin_4": { "minecraft:skin_id": { "value": 4 } },
      "villager_skin_5": { "minecraft:skin_id": { "value": 5 } },
      "rza:attack_behaviors": {
        "minecraft:behavior.nearest_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "within_radius": 16,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "player" },
                  { "test": "is_visible", "subject": "self", "value": true }
                ]
              }
            },
            {
              "filters": {
                "all_of": [
                  { "test": "is_visible", "subject": "other", "value": true },
                  { "none_of": [{ "test": "has_mob_effect", "subject": "self", "value": "weakness" }] },
                  {
                    "any_of": [
                      { "test": "is_family", "subject": "other", "value": "snowgolem" },
                      { "test": "is_family", "subject": "other", "value": "irongolem" },
                      { "test": "is_family", "subject": "other", "value": "villager" },
                      { "test": "is_family", "subject": "other", "value": "wandering_trader" },
                      { "test": "is_family", "subject": "other", "value": "illager" }
                    ]
                  }
                ]
              },
              "max_dist": 24
            }
          ]
        },
        "minecraft:behavior.melee_attack": { "priority": 3, "speed_multiplier": 1.0, "track_target": true },
        "minecraft:attack": { "damage": 3 }
      }
    },
    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:navigation.walk": { "is_amphibious": true, "can_pass_doors": true, "can_break_doors": true, "avoid_sun": false },
      "minecraft:movement.basic": {},
      "minecraft:jump.static": {},
      "minecraft:can_climb": {},
      "minecraft:breathable": { "total_supply": 15, "suffocate_time": 0, "breathes_water": true },
      "minecraft:loot": { "table": "loot_tables/entities/zombie.json" },
      "minecraft:equip_item": { "excluded_items": [{ "item": "minecraft:banner:15" }] },
      "minecraft:conditional_bandwidth_optimization": {},
      "minecraft:collision_box": { "width": 0.6, "height": 1.9 },
      "minecraft:health": { "value": 20, "max": 20 },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          { "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true }, "cause": "lava", "damage_per_tick": 4 }
        ]
      },
      "minecraft:attack": { "damage": 3 },
      "minecraft:nameable": {},
      "minecraft:behavior.delayed_attack": {
        "priority": 3,
        "attack_duration": 0.5,
        "hit_delay_pct": 0.85,
        "max_path_time": 1,
        "random_stop_interval": 120,
        "x_max_rotation": 30
      },
      "minecraft:shareables": {
        "singular_pickup": true,
        "items": [
          { "item": "minecraft:netherite_sword", "want_amount": 1, "surplus_amount": 1, "priority": 0 },
          { "item": "minecraft:diamond_sword", "want_amount": 1, "surplus_amount": 1, "priority": 1 },
          { "item": "minecraft:iron_sword", "want_amount": 1, "surplus_amount": 1, "priority": 2 },
          { "item": "minecraft:stone_sword", "want_amount": 1, "surplus_amount": 1, "priority": 3 },
          { "item": "minecraft:golden_sword", "want_amount": 1, "surplus_amount": 1, "priority": 4 },
          { "item": "minecraft:wooden_sword", "want_amount": 1, "surplus_amount": 1, "priority": 5 },
          { "item": "minecraft:netherite_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 0 },
          { "item": "minecraft:diamond_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 1 },
          { "item": "minecraft:iron_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 2 },
          { "item": "minecraft:chainmail_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 3 },
          { "item": "minecraft:golden_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 4 },
          { "item": "minecraft:leather_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 5 },
          { "item": "minecraft:turtle_helmet", "want_amount": 1, "surplus_amount": 1, "priority": 6 },
          { "item": "minecraft:skull:0", "want_amount": 1, "surplus_amount": 1, "priority": 7 },
          { "item": "minecraft:skull:1", "want_amount": 1, "surplus_amount": 1, "priority": 7 },
          { "item": "minecraft:carved_pumpkin", "want_amount": 1, "surplus_amount": 1, "priority": 7 },
          { "item": "minecraft:netherite_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 0 },
          { "item": "minecraft:diamond_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 1 },
          { "item": "minecraft:iron_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 2 },
          { "item": "minecraft:chainmail_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 3 },
          { "item": "minecraft:golden_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 4 },
          { "item": "minecraft:leather_chestplate", "want_amount": 1, "surplus_amount": 1, "priority": 5 },
          { "item": "minecraft:netherite_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 0 },
          { "item": "minecraft:diamond_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 1 },
          { "item": "minecraft:iron_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 2 },
          { "item": "minecraft:chainmail_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 3 },
          { "item": "minecraft:golden_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 4 },
          { "item": "minecraft:leather_leggings", "want_amount": 1, "surplus_amount": 1, "priority": 5 },
          { "item": "minecraft:netherite_boots", "want_amount": 1, "surplus_amount": 1, "priority": 0 },
          { "item": "minecraft:diamond_boots", "want_amount": 1, "surplus_amount": 1, "priority": 1 },
          { "item": "minecraft:iron_boots", "want_amount": 1, "surplus_amount": 1, "priority": 2 },
          { "item": "minecraft:chainmail_boots", "want_amount": 1, "surplus_amount": 1, "priority": 3 },
          { "item": "minecraft:golden_boots", "want_amount": 1, "surplus_amount": 1, "priority": 4 },
          { "item": "minecraft:leather_boots", "want_amount": 1, "surplus_amount": 1, "priority": 5 }
        ]
      },
      "minecraft:interact": {
        "interactions": {
          "on_interact": {
            "filters": {
              "all_of": [
                { "test": "has_equipment", "domain": "hand", "subject": "other", "value": "golden_apple" },
                { "test": "has_component", "subject": "self", "value": "minecraft:effect.weakness" }
              ]
            },
            "event": "villager_converted",
            "target": "self"
          },
          "use_item": true,
          "interact_text": "action.interact.cure"
        }
      },
      "minecraft:despawn": { "despawn_from_distance": {} },
      "minecraft:behavior.melee_attack": { "can_spread_on_fire": true, "priority": 6 },
      "minecraft:behavior.equip_item": { "priority": 3 },
      "minecraft:behavior.stomp_turtle_egg": {
        "priority": 4,
        "speed_multiplier": 1,
        "search_range": 10,
        "search_height": 2,
        "goal_radius": 1.14,
        "interval": 20
      },
      "minecraft:behavior.pickup_items": {
        "priority": 8,
        "max_dist": 3,
        "goal_radius": 2,
        "speed_multiplier": 1,
        "pickup_based_on_chance": true,
        "can_pickup_any_item": true,
        "excluded_items": ["minecraft:glow_ink_sac"]
      },
      "minecraft:behavior.random_stroll": { "priority": 9, "speed_multiplier": 1 },
      "minecraft:behavior.look_at_player": { "priority": 10, "look_distance": 6, "probability": 0.02 },
      "minecraft:behavior.random_look_around": { "priority": 11 },
      "minecraft:behavior.hurt_by_target": {
        "priority": 1,
        "alert_same_type": true,
        "entity_types": {
          "filters": {
            "all_of": [
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "turret" },
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "villager" },
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "illager" },
              { "test": "is_visible", "subject": "other", "value": true }
            ]
          }
        }
      },
      "minecraft:physics": {},
      "minecraft:pushable": { "is_pushable": true, "is_pushable_by_piston": true },
      "minecraft:entity_sensor": {
        "subsensors": [
          {
            "range": [4.0, 4.0],
            "event_filters": {
              "all_of": [
                { "test": "is_family", "subject": "other", "value": "villager" },
                { "test": "has_mob_effect", "subject": "self", "value": "weakness" },
                { "test": "has_component", "subject": "self", "operator": "!=", "value": "minecraft:transformation" }
              ]
            },
            "event": "villager_converted"
          }
        ]
      },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "on_damage": { "filters": { "all_of": [{ "any_of": [{ "test": "is_family", "subject": "other", "value": "zombie" }] }] } },
            "deals_damage": "no"
          },
          {
            "on_damage": {
              "filters": {
                "all_of": [
                  { "test": "has_component", "subject": "self", "value": "minecraft:transformation" },
                  { "test": "has_mob_effect", "subject": "self", "value": "weakness" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "player" }
                ]
              }
            },
            "deals_damage": "no_but_side_effects_apply"
          }
        ]
      }
    },
    "events": {
      "villager_converted": { "remove": { "component_groups": ["rza:attack_behaviors"] }, "add": { "component_groups": ["to_villager"] } },
      "minecraft:entity_spawned": {
        "sequence": [
          { "add": { "component_groups": ["rza:attack_behaviors"] } },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:variant" },
            "randomize": [
              { "weight": 9500, "remove": {}, "add": { "component_groups": ["adult"] } },
              { "weight": 425, "remove": {}, "add": { "component_groups": ["baby"] } },
              { "weight": 75, "remove": {}, "add": { "component_groups": ["baby", "jockey"] } }
            ]
          },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:variant" },
            "randomize": [
              { "weight": 1, "add": { "component_groups": ["unskilled"] } },
              { "weight": 1, "add": { "component_groups": ["nitwit"] } },
              { "weight": 1, "add": { "component_groups": ["farmer"] } },
              { "weight": 1, "add": { "component_groups": ["fisherman"] } },
              { "weight": 1, "add": { "component_groups": ["shepherd"] } },
              { "weight": 1, "add": { "component_groups": ["fletcher"] } },
              { "weight": 1, "add": { "component_groups": ["librarian"] } },
              { "weight": 1, "add": { "component_groups": ["cartographer"] } },
              { "weight": 1, "add": { "component_groups": ["cleric"] } },
              { "weight": 1, "add": { "component_groups": ["armorer"] } },
              { "weight": 1, "add": { "component_groups": ["weaponsmith"] } },
              { "weight": 1, "add": { "component_groups": ["toolsmith"] } },
              { "weight": 1, "add": { "component_groups": ["butcher"] } },
              { "weight": 1, "add": { "component_groups": ["leatherworker"] } },
              { "weight": 1, "add": { "component_groups": ["mason"] } }
            ]
          },
          { "trigger": "minecraft:add_biome_and_skin" },
          { "randomize": [{ "weight": 10, "add": { "component_groups": ["can_break_doors"] } }, { "weight": 90 }] }
        ]
      },
      "minecraft:spawn_skilled_adult": {
        "sequence": [
          { "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:variant" }, "add": { "component_groups": ["adult"] } },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:variant" },
            "randomize": [
              { "weight": 1, "add": { "component_groups": ["farmer"] } },
              { "weight": 1, "add": { "component_groups": ["fisherman"] } },
              { "weight": 1, "add": { "component_groups": ["shepherd"] } },
              { "weight": 1, "add": { "component_groups": ["fletcher"] } },
              { "weight": 1, "add": { "component_groups": ["librarian"] } },
              { "weight": 1, "add": { "component_groups": ["cartographer"] } },
              { "weight": 1, "add": { "component_groups": ["cleric"] } },
              { "weight": 1, "add": { "component_groups": ["armorer"] } },
              { "weight": 1, "add": { "component_groups": ["weaponsmith"] } },
              { "weight": 1, "add": { "component_groups": ["toolsmith"] } },
              { "weight": 1, "add": { "component_groups": ["butcher"] } },
              { "weight": 1, "add": { "component_groups": ["leatherworker"] } },
              { "weight": 1, "add": { "component_groups": ["mason"] } }
            ]
          },
          { "trigger": "minecraft:add_biome_and_skin" }
        ]
      },
      "minecraft:entity_transformed": {
        "sequence": [
          { "add": { "component_groups": ["rza:attack_behaviors"] } },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:variant" },
            "sequence": [
              // Transform baby villager to baby zombie
              { "filters": { "test": "has_component", "subject": "other", "value": "minecraft:is_baby" }, "add": { "component_groups": ["baby"] } },
              // Transform adult villager to adult zombie
              {
                "filters": { "test": "has_component", "subject": "other", "operator": "!=", "value": "minecraft:is_baby" },
                "add": { "component_groups": ["adult"] }
              },
              // Transform unskilled zombie to unskilled villager
              { "filters": { "test": "is_family", "subject": "other", "value": "unskilled" }, "add": { "component_groups": ["unskilled"] } },
              // Transform nitwit zombie to nitwit villager
              { "filters": { "test": "is_family", "subject": "other", "value": "nitwit" }, "add": { "component_groups": ["nitwit"] } },
              // Transform farmer zombie to farmer villager
              { "filters": { "test": "is_family", "subject": "other", "value": "farmer" }, "add": { "component_groups": ["farmer"] } },
              // Transform fisherman zombie to fisherman villager
              { "filters": { "test": "is_family", "subject": "other", "value": "fisherman" }, "add": { "component_groups": ["fisherman"] } },
              // Transform shepherd zombie to shepherd villager
              { "filters": { "test": "is_family", "subject": "other", "value": "shepherd" }, "add": { "component_groups": ["shepherd"] } },
              // Transform fletcher zombie to fletcher villager
              { "filters": { "test": "is_family", "subject": "other", "value": "fletcher" }, "add": { "component_groups": ["fletcher"] } },
              // Transform librarian zombie to librarian villager
              { "filters": { "test": "is_family", "subject": "other", "value": "librarian" }, "add": { "component_groups": ["librarian"] } },
              // Transform cartographer zombie to cartographer villager
              { "filters": { "test": "is_family", "subject": "other", "value": "cartographer" }, "add": { "component_groups": ["cartographer"] } },
              // Transform cleric zombie to cleric villager
              { "filters": { "test": "is_family", "subject": "other", "value": "cleric" }, "add": { "component_groups": ["cleric"] } },
              // Transform armorer zombie to armorer villager
              { "filters": { "test": "is_family", "subject": "other", "value": "armorer" }, "add": { "component_groups": ["armorer"] } },
              // Transform weaponsmith zombie to weaponsmith villager
              { "filters": { "test": "is_family", "subject": "other", "value": "weaponsmith" }, "add": { "component_groups": ["weaponsmith"] } },
              // Transform toolsmith zombie to toolsmith villager
              { "filters": { "test": "is_family", "subject": "other", "value": "toolsmith" }, "add": { "component_groups": ["toolsmith"] } },
              // Transform butcher zombie to butcher villager
              { "filters": { "test": "is_family", "subject": "other", "value": "butcher" }, "add": { "component_groups": ["butcher"] } },
              // Transform leatherworker zombie to leatherworker villager
              { "filters": { "test": "is_family", "subject": "other", "value": "leatherworker" }, "add": { "component_groups": ["leatherworker"] } },
              // Transform mason zombie to mason villager
              { "filters": { "test": "is_family", "subject": "other", "value": "stone_mason" }, "add": { "component_groups": ["mason"] } }
            ]
          },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:mark_variant" },
            "sequence": [
              // Transform desert zombie to desert villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 1 }, "add": { "component_groups": ["desert_villager"] } },
              // Transform jungle zombie to jungle villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 2 }, "add": { "component_groups": ["jungle_villager"] } },
              // Transform jungle zombie to jungle villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 3 }, "add": { "component_groups": ["savanna_villager"] } },
              // Transform jungle zombie to jungle villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 4 }, "add": { "component_groups": ["snow_villager"] } },
              // Transform jungle zombie to jungle villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 5 }, "add": { "component_groups": ["swamp_villager"] } },
              // Transform taiga zombie to taiga villager
              { "filters": { "test": "is_mark_variant", "subject": "other", "value": 6 }, "add": { "component_groups": ["taiga_villager"] } }
            ]
          },
          {
            "filters": { "test": "has_component", "operator": "!=", "value": "minecraft:skin_id" },
            "sequence": [
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 0 }, "add": { "component_groups": ["villager_skin_0"] } },
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 1 }, "add": { "component_groups": ["villager_skin_1"] } },
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 2 }, "add": { "component_groups": ["villager_skin_2"] } },
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 3 }, "add": { "component_groups": ["villager_skin_3"] } },
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 4 }, "add": { "component_groups": ["villager_skin_4"] } },
              { "filters": { "test": "is_skin_id", "subject": "other", "value": 5 }, "add": { "component_groups": ["villager_skin_5"] } }
            ]
          }
        ]
      },
      "minecraft:become_cleric": { "add": { "component_groups": ["cleric"] } },
      "from_village": { "sequence": [{ "trigger": "minecraft:entity_spawned" }, { "add": { "component_groups": ["from_abandoned_village"] } }] },
      "minecraft:add_biome_and_skin": {
        "sequence": [
          {
            "randomize": [
              { "weight": 1, "add": { "component_groups": ["villager_skin_0"] } },
              { "weight": 1, "add": { "component_groups": ["villager_skin_1"] } },
              { "weight": 1, "add": { "component_groups": ["villager_skin_2"] } },
              { "weight": 1, "add": { "component_groups": ["villager_skin_3"] } },
              { "weight": 1, "add": { "component_groups": ["villager_skin_4"] } },
              { "weight": 1, "add": { "component_groups": ["villager_skin_5"] } }
            ]
          },
          {
            "filters": {
              "any_of": [
                { "test": "has_biome_tag", "value": "desert" },
                { "test": "has_biome_tag", "value": "mesa" }
              ]
            },
            "add": { "component_groups": ["desert_villager"] }
          },
          { "filters": { "test": "has_biome_tag", "value": "jungle" }, "add": { "component_groups": ["jungle_villager"] } },
          { "filters": { "test": "has_biome_tag", "value": "savanna" }, "add": { "component_groups": ["savanna_villager"] } },
          {
            "filters": {
              "any_of": [
                {
                  "all_of": [
                    { "test": "has_biome_tag", "value": "cold" },
                    { "test": "has_biome_tag", "operator": "!=", "value": "ocean" }
                  ]
                },
                { "test": "has_biome_tag", "value": "frozen" }
              ]
            },
            "add": { "component_groups": ["snow_villager"] }
          },
          { "filters": { "test": "has_biome_tag", "value": "swamp" }, "add": { "component_groups": ["swamp_villager"] } },
          {
            "filters": {
              "all_of": [
                {
                  "any_of": [
                    { "test": "has_biome_tag", "value": "taiga" },
                    { "test": "has_biome_tag", "value": "extreme_hills" }
                  ]
                },
                { "test": "has_biome_tag", "operator": "!=", "value": "cold" }
              ]
            },
            "add": { "component_groups": ["taiga_villager"] }
          }
        ]
      }
    }
  }
}
