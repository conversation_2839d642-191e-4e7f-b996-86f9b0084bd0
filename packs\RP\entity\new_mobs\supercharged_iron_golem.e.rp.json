{"format_version": "1.10.0", "minecraft:client_entity": {"description": {"identifier": "rza:supercharged_iron_golem", "materials": {"default": "iron_golem"}, "textures": {"default": "textures/entity/iron_golem/super_iron_golem", "cracked_high": "textures/entity/iron_golem/cracked_high", "cracked_med": "textures/entity/iron_golem/cracked_medium", "cracked_low": "textures/entity/iron_golem/cracked_low", "cracked_none": "textures/entity/iron_golem/cracked_none", "super": "textures/entity/iron_golem/super_iron_golem_lvl"}, "geometry": {"default": "geometry.iron_golem.new"}, "animations": {"walk": "animation.irongolem.walk", "idle": "animation.irongolem.idle", "has_target": "animation.irongolem.has_target", "smash": "animation.supercharged_iron_golem.smash", "flower": "animation.irongolem.flower", "death": "animation.charged_irongolem.death", "look_at_target": "animation.common.look_at_target", "particle": "animation.super_iron_golem.particle", "general": "controller.animation.supercharged_iron_golem.general"}, "scripts": {"pre_animation": ["variable.modified_tcos0 = Math.clamp(((Math.cos(query.modified_distance_moved * 13.5) * Math.min(query.modified_move_speed, 0.6) / variable.gliding_speed_value) * 25.0), -12.5, 12.5);"], "animate": ["general", "particle", {"look_at_target": "query.is_alive"}, {"death": "!query.is_alive"}]}, "particle_effects": {"supercharged_iron_golem_particle": "rza:supercharged_iron_golem_particle"}, "render_controllers": ["controller.render.iron_golem", "controller.render.iron_golem_cracks", "controller.render.iron_golem.super"], "spawn_egg": {"base_color": "#A7A2A2", "overlay_color": "#75B058"}}}}