import { BlockCustomComponent, BlockComponentTickEvent, system } from "@minecraft/server";
import { blockFeatures } from "./blocks";

/**
 * Custom block component for handling block ticking behavior
 * Implements the new BlockCustomComponent interface
 */
class RzaTickComponent implements BlockCustomComponent {
  /**
   * Called when the block receives a tick from the game engine
   * @param event The tick event data
   * @param params Custom component parameters (unused in this implementation)
   */
  onTick(event: BlockComponentTickEvent): void {
    const block = event.block;
    const blockTypeId = block.type.id;

    blockFeatures(block, blockTypeId);
  }
}

/**
 * Registers all custom block components for the addon using the new system
 * Uses world.beforeEvents.worldInitialize for proper registration timing
 */
system.beforeEvents.startup.subscribe((initEvent) => {
  // Register the tick component using the new class-based approach
  initEvent.blockComponentRegistry.registerCustomComponent("rza:tick", new RzaTickComponent());
});
