{"format_version": "1.13.0", "minecraft:attachable": {"description": {"identifier": "rza:flamethrower", "materials": {"default": "entity_alphatest"}, "textures": {"default": "textures/attachables/weapons/flamethrower"}, "geometry": {"default": "geometry.flamethrower"}, "animations": {"first_person_hold": "animation.flamethrower.first_person_hold", "third_person_hold": "animation.flamethrower.third_person_hold", "fire": "animation.flamethrower.fire", "fire_controller": "controller.animation.attachable.fire", "general": "controller.animation.attachable.general"}, "scripts": {"animate": ["general", "fire_controller"]}, "render_controllers": ["controller.render.model_default"]}}}