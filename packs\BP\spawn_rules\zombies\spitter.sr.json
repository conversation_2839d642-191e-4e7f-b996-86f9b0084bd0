{
  "format_version": "1.10.0",
  "minecraft:spawn_rules": {
    "description": { "identifier": "rza:spitter", "population_control": "monster" },
    "conditions": [
      {
        "minecraft:spawns_on_surface": {},
        "minecraft:brightness_filter": { "min": 0, "max": 15, "adjust_for_weather": false },
        "minecraft:difficulty_filter": { "min": "easy", "max": "hard" },
        "minecraft:world_age_filter": {
          "min": 60000 //Day 50
        },
        "minecraft:distance_filter": { "min": 32, "max": 96 },
        "minecraft:weight": { "default": 30 },
        "minecraft:herd": { "min_size": 1, "max_size": 2 },
        "minecraft:spawns_on_block_filter": ["dirt", "stone", "grass_block"],
        "minecraft:biome_filter": { "test": "has_biome_tag", "operator": "==", "value": "monster" }
      }
    ]
  }
}
