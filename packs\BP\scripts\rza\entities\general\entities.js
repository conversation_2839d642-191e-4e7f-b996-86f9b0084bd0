import { EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { stormWeavers, stormWeaverLightning, fireLightningStrike } from "../../turrets/stormWeaver";
import { pulsarSystemMechanics, pulsarSystems } from "../../turrets/pulsarSystem";
import { repairArrayCooldown, repairArrayMechanics } from "../../turrets/repairArray";
import { meleeWeaponCooldown, nonPlayerMeleeWeaponAttack, playerMeleeWeaponAttack } from "../../weapons/melee";
import { cleanupAcidPuddleCooldown, spitterAcidPuddleEffect } from "../zombies/spitter";
import { sonicCannonHit, fireSonicCharge } from "../../turrets/sonicCannon";
import { ferralLeap } from "../zombies/feral";
import { alphaZombieMechanics } from "../zombies/alpha";
import { witheratorMechanics, witheratorSkullHit } from "../../turrets/witherator";
import { system } from "@minecraft/server";
import { pyroChargerFireball } from "../../turrets/pyroCharger";
import { handleTurretConfiguration } from "../../turrets/targetConfig";
import { flamethrowerFireball } from "../../weapons/flamethrower";
import { createShockwave } from "./general";
import { fireLaserPulse } from "rza/turrets/laserTurret";
import { transformToWalker, transformToFeral } from "../zombies/general";
const vanillaMobTransformations = [
    "minecraft:creeper",
    "minecraft:enderman",
    "minecraft:skeleton",
    "minecraft:spider",
    "minecraft:stray",
    "minecraft:drowned",
    "minecraft:zombie",
    "minecraft:husk"
];
export function handleVanillaMobTransformation(entity) {
    const entityType = entity.typeId;
    try {
        if (vanillaMobTransformations.includes(entityType)) {
            transformToWalker(entity, entity.dimension, entity.location);
        }
        else if (entityType === "minecraft:witch") {
            transformToFeral(entity, entity.dimension, entity.location);
        }
    }
    catch (e) { }
    return;
}
export function handleEntityRemoveCleanup(entityType, entityId) {
    if (entityType === "rza:storm_weaver") {
        stormWeavers["rza:destroyed_weavers"].delete(entityId);
        stormWeavers["rza:chain_length"].delete(entityId);
        stormWeavers["rza:chained_zombies"].delete(entityId);
    }
    if (pulsarSystems["rza:cooldown"].has(entityId))
        pulsarSystems["rza:cooldown"].delete(entityId);
    if (pulsarSystems["rza:fire_time"].has(entityId))
        pulsarSystems["rza:fire_time"].delete(entityId);
    if (pulsarSystems["rza:pulse_radius_offset"].has(entityId))
        pulsarSystems["rza:pulse_radius_offset"].delete(entityId);
    if (repairArrayCooldown.has(entityId))
        repairArrayCooldown.delete(entityId);
    if (entityId == "minecraft:pillager" || entityId == "minecraft:vindicator") {
        meleeWeaponCooldown.delete(entityId);
    }
    if (entityType === "rza:spitter_acid_puddle_normal" || entityType === "rza:spitter_acid_puddle_mutated") {
        cleanupAcidPuddleCooldown(entityId);
    }
    return;
}
export function handleEntityHurtEvent(entity, source, sourceId, damage, isZombie) {
    if (sourceId === "minecraft:player" && source) {
        const weapon = source.getComponent(EntityComponentTypes.Equippable)?.getEquipment(EquipmentSlot.Mainhand);
        if (weapon?.type.id.endsWith("mace")) {
            playerMeleeWeaponAttack(entity, source, weapon, damage);
        }
    }
    if (sourceId === "rza:sonic_cannon" && isZombie && source) {
        let run = system.run(() => {
            sonicCannonHit(entity, source);
            system.clearRun(run);
        });
    }
    if (sourceId === "rza:storm_weaver" && isZombie && source) {
        let run = system.runTimeout(() => {
            stormWeaverLightning(entity, source);
            system.clearRun(run);
        }, 2);
    }
}
export function handleEntityHitEntityEvent(data) {
    const entity = data.hitEntity;
    const source = data.damagingEntity;
    const sourceId = source.typeId;
    const isZombie = source.hasComponent(EntityComponentTypes.TypeFamily) &&
        source.getComponent(EntityComponentTypes.TypeFamily).hasTypeFamily("zombie");
    if (!isZombie) {
        const cooldown = meleeWeaponCooldown.get(source.id);
        const isPlayer = sourceId == "minecraft:player";
        const isNonPlayer = sourceId == "minecraft:pillager" || sourceId == "minecraft:vindicator";
        if (isPlayer && cooldown == 0) {
            const weapon = source.getComponent(EntityComponentTypes.Equippable).getEquipment(EquipmentSlot.Mainhand);
            if (weapon && (weapon.type.id.endsWith("axe") || weapon.type.id.endsWith("sword"))) {
                let run = system.run(() => {
                    playerMeleeWeaponAttack(entity, source, weapon);
                    system.clearRun(run);
                });
            }
        }
        else if (isNonPlayer && cooldown == 0) {
            let run = system.run(() => {
                nonPlayerMeleeWeaponAttack(entity, source);
                system.clearRun(run);
            });
        }
        else if (sourceId == "rza:supercharged_iron_golem") {
            createShockwave(source, 3, 1.2, 0.5, 1, ["zombie"], "rza:ground_slam");
        }
    }
    return;
}
export function handleEntityTriggerEvents(data) {
    const event = data.eventId;
    const entity = data.entity;
    switch (event) {
        case "rza:leap":
            let leapRun = system.run(() => {
                ferralLeap(entity);
                system.clearRun(leapRun);
            });
            break;
        case "rza:sonic_charge":
            let chargeRun = system.run(() => {
                fireSonicCharge(entity);
                system.clearRun(chargeRun);
            });
            break;
        case "rza:laser_pulse":
            fireLaserPulse(entity);
            break;
        case "rza:lightning_strike":
            let strikeRun = system.run(() => {
                fireLightningStrike(entity);
                system.clearRun(strikeRun);
            });
            break;
        case "rza:explode":
            let explodeRun = system.run(() => {
                const id = entity.id;
                witheratorSkullHit(entity, id);
                system.clearRun(explodeRun);
            });
            break;
        case "rza:alpha_zombie_buff":
            let buffRun = system.run(() => {
                alphaZombieMechanics(entity);
                system.clearRun(buffRun);
            });
            break;
        case "rza:configure":
            const isTurret = entity.hasComponent(EntityComponentTypes.TypeFamily) &&
                entity.getComponent(EntityComponentTypes.TypeFamily).hasTypeFamily("turret");
            if (isTurret) {
                handleTurretConfiguration(entity);
            }
            break;
    }
    return;
}
const entityHandlers = new Map([
    ["rza:pyro_charger_fireball", pyroChargerFireball],
    ["rza:flamethrower_fireball", flamethrowerFireball],
    ["rza:pulsar_system", pulsarSystemMechanics],
    ["rza:repair_array", repairArrayMechanics],
    ["rza:witherator", witheratorMechanics],
    ["rza:spitter_acid_puddle_normal", spitterAcidPuddleEffect],
    ["rza:spitter_acid_puddle_mutated", spitterAcidPuddleEffect]
]);
export function mainEntityFeatures(entity) {
    const handler = entityHandlers.get(entity.typeId);
    if (handler) {
        const run = system.run(() => {
            handler(entity);
            system.clearRun(run);
        });
    }
    return;
}
