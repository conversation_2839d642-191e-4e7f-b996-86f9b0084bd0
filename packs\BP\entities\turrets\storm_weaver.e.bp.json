{
  "format_version": "1.21.90",
  "minecraft:entity": {
    "description": {
      "identifier": "rza:storm_weaver",
      "is_spawnable": false,
      "is_summonable": true,
      "is_experimental": false,
      "properties": {
        "rza:active": { "type": "bool", "client_sync": true, "default": false },
        "rza:prioritize_mutants": { "client_sync": true, "type": "bool", "default": false },
        "rza:target_zombies": {
          "client_sync": true,
          "type": "enum",
          "default": "Walkers",
          "values": ["Walkers", "Miners", "Ferals", "Spitters", "Alphas"]
        }
      },
      "animations": { "general": "controller.animation.storm_weaver.general" },
      "scripts": { "animate": ["general"] }
    },
    "component_groups": {
      "rza:recoil_recovery": { "minecraft:timer": { "time": 9, "looping": false, "time_down_event": { "event": "rza:recover", "target": "self" } } },
      "rza:active": {
        "minecraft:behavior.random_look_around": { "priority": 4 },
        "minecraft:behavior.ranged_attack": {
          "priority": 0,
          "attack_interval": 1,
          "attack_radius": 24,
          "swing": true,
          "x_max_rotation": 15,
          "ranged_fov": 45
        },
        "minecraft:shooter": { "def": "rza:storm_weaver_lightning" }
      },
      //Walkers: Don't Prioritize Mutated Walkers
      "rza:target_walkers": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "walker" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "walker" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Walkers: Prioritize Mutated Walkers
      "rza:target_walkers_prioritize_mutants": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "walker" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": true }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "walker" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": false }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 2,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "walker" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Miners: Don't Prioritize Mutated Miners
      "rza:target_miners": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "miner" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "miner" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Miners: Prioritize Mutated Miners
      "rza:target_miners_prioritize_mutants": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "miner" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": true }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "miner" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": false }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 2,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "miner" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Ferals: Don't Prioritize Mutated Ferals
      "rza:target_ferals": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "feral" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "feral" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Ferals: Prioritize Mutated Ferals
      "rza:target_ferals_prioritize_mutants": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "feral" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": true }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "feral" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": false }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 2,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "feral" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Spitters: Don't Prioritize Mutated Spitters
      "rza:target_spitters": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "spitter" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "spitter" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Spitters: Prioritize Mutated Spitters
      "rza:target_spitters_prioritize_mutants": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "spitter" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": true }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "spitter" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": false }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 2,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "spitter" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Alphas: Don't Prioritize Mutated Alphas
      "rza:target_alphas": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "alpha" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "alpha" },
                  { "test": "has_tag", "subject": "other", "operator": "!=", "value": "chainer" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      },
      //Alphas: Prioritize Mutated Alphas
      "rza:target_alphas_prioritize_mutants": {
        "minecraft:behavior.nearest_prioritized_attackable_target": {
          "priority": 2,
          "must_see": true,
          "reselect_targets": true,
          "must_see_forget_duration": 0,
          "entity_types": [
            {
              "priority": 0,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "alpha" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": true }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 1,
              "reevaluate_description": true,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "value": "alpha" },
                  { "test": "bool_property", "subject": "other", "domain": "rza:mutated", "value": false }
                ]
              },
              "max_dist": 24
            },
            {
              "priority": 2,
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "zombie" },
                  { "test": "is_family", "subject": "other", "operator": "!=", "value": "alpha" }
                ]
              },
              "max_dist": 24
            }
          ]
        }
      }
    },
    "events": {
      "minecraft:entity_spawned": { "sequence": [{ "queue_command": { "command": "playsound random.anvil_break @a ~ ~ ~" } }] },
      "rza:activate": {
        "sequence": [
          { "set_property": { "rza:active": true } },
          { "add": { "component_groups": ["rza:active", "rza:target_walkers"] } },
          { "trigger": { "event": "rza:start_recoil_recovery", "target": "self" } }
        ]
      },
      "rza:deactivate": {
        "sequence": [
          { "set_property": { "rza:active": false } },
          {
            "remove": {
              "component_groups": [
                "rza:active",
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            }
          }
        ]
      },
      "rza:lightning_strike": {},
      "rza:configure": {},
      "rza:target_walkers": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_walkers"] }
          }
        ]
      },
      "rza:target_walkers_prioritize_mutants": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_walkers_prioritize_mutants"] }
          }
        ]
      },
      "rza:target_miners": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_miners"] }
          }
        ]
      },
      "rza:target_miners_prioritize_mutants": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_miners_prioritize_mutants"] }
          }
        ]
      },
      "rza:target_ferals": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_ferals"] }
          }
        ]
      },
      "rza:target_ferals_prioritize_mutants": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_ferals_prioritize_mutants"] }
          }
        ]
      },
      "rza:target_spitters": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_spitters"] }
          }
        ]
      },
      "rza:target_spitters_prioritize_mutants": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters"
              ]
            },
            "add": { "component_groups": ["rza:target_spitters_prioritize_mutants"] }
          }
        ]
      },
      "rza:target_alphas": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas_prioritize_mutants"
              ]
            },
            "add": { "component_groups": ["rza:target_alphas"] }
          }
        ]
      },
      "rza:target_alphas_prioritize_mutants": {
        "sequence": [
          {
            "remove": {
              "component_groups": [
                "rza:target_walkers",
                "rza:target_walkers_prioritize_mutants",
                "rza:target_miners",
                "rza:target_miners_prioritize_mutants",
                "rza:target_ferals",
                "rza:target_ferals_prioritize_mutants",
                "rza:target_spitters",
                "rza:target_spitters_prioritize_mutants",
                "rza:target_alphas"
              ]
            },
            "add": { "component_groups": ["rza:target_alphas_prioritize_mutants"] }
          }
        ]
      },
      "rza:start_recoil_recovery": { "remove": { "component_groups": ["rza:active"] }, "add": { "component_groups": ["rza:recoil_recovery"] } },
      "rza:recover": { "remove": { "component_groups": ["rza:recoil_recovery"] }, "add": { "component_groups": ["rza:active"] } },
      "rza:despawn": { "queue_command": { "command": "function world/turrets/turret_drop/storm_weaver" } }
    },
    "components": {
      "minecraft:type_family": { "family": ["turret", "storm_weaver"] },
      "minecraft:collision_box": { "width": 0.8, "height": 1.3 },
      "minecraft:persistent": {},
      "minecraft:health": { "value": 270 },
      "minecraft:movement": { "value": 0 },
      "minecraft:interact": {
        "interactions": [
          {
            "on_interact": {
              "filters": {
                "all_of": [
                  { "test": "is_family", "subject": "other", "value": "player" },
                  { "test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot" },
                  { "test": "is_missing_health", "value": true }
                ]
              }
            },
            "use_item": true,
            "swing": true,
            "health_amount": 2,
            "play_sounds": "irongolem.repair",
            "interact_text": "action.interact.repair"
          },
          {
            "interact_text": "action.hint.turret.configure",
            "on_interact": {
              "filters": { "test": "has_equipment", "domain": "hand", "subject": "other", "operator": "!=", "value": "iron_ingot" },
              "event": "rza:configure",
              "target": "self"
            }
          }
        ]
      },
      "minecraft:navigation.walk": { "is_amphibious": true, "avoid_water": true },
      "minecraft:movement.basic": { "max_turn": 0.05 },
      "minecraft:damage_sensor": {
        "triggers": [
          {
            "cause": "entity_attack",
            "on_damage": {
              "filters": { "test": "is_family", "subject": "other", "operator": "==", "value": "player" },
              "event": "rza:despawn",
              "target": "self"
            },
            "deals_damage": "no"
          },
          { "on_damage": { "filters": { "test": "is_family", "subject": "other", "value": "turret" } }, "deals_damage": "no" },
          { "on_damage": { "filters": { "test": "is_family", "subject": "other", "value": "villager" } }, "deals_damage": "no" },
          { "on_damage": { "filters": { "test": "is_family", "subject": "other", "value": "illager" } }, "deals_damage": "no" }
        ]
      },
      "minecraft:follow_range": { "value": 64 },
      "minecraft:knockback_resistance": { "value": 1 },
      "minecraft:pushable": { "is_pushable": false, "is_pushable_by_piston": false },
      "minecraft:physics": {},
      "minecraft:conditional_bandwidth_optimization": {}
    }
  }
}
