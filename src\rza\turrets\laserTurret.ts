import { <PERSON>ti<PERSON>, <PERSON>tityD<PERSON>geC<PERSON>e, Vector3 } from "@minecraft/server";
import { fixedLenRaycast } from "./raycast";

/**
 * Converts entity rotation (yaw and pitch) to a normalized direction vector
 * @param rotation The entity's rotation with yaw (y) and pitch (x) in degrees
 * @returns A normalized Vector3 representing the direction
 */
function rotationToDirection(rotation: { x: number; y: number }): Vector3 {
  // Convert degrees to radians
  const yawRad = (rotation.y * Math.PI) / 180;
  const pitchRad = (rotation.x * Math.PI) / 180;

  // Calculate direction vector from rotation
  // x = -sin(yaw) * cos(pitch)
  // y = -sin(pitch)
  // z = cos(yaw) * cos(pitch)
  const direction: Vector3 = { x: -Math.sin(yawRad) * Math.cos(pitchRad), y: -Math.sin(pitchRad), z: Math.cos(yawRad) * Math.cos(pitchRad) };

  return direction;
}

/**
 * Fires a laser pulse using raycast based on turret rotation
 * @param laserTurret The laserTurret firing the laser pulse
 */
export function fireLaserPulse(laserTurret: Entity): void {
  const dimension = laserTurret.dimension;
  const location = laserTurret.location;
  const rotation = laserTurret.getRotation();

  // Calculate direction from turret rotation
  const direction = rotationToDirection(rotation);

  const startOffset = 2;
  const startPos: Vector3 = {
    x: location.x + direction.x * startOffset,
    y: location.y + 0.65 + direction.y * startOffset,
    z: location.z + direction.z * startOffset
  };

  // Get ray positions with fixed length raycast (32 blocks)
  const positions = fixedLenRaycast(startPos, direction, 32, 0.75);

  // Handle particles and damage for each position
  for (const pos of positions) {
    try {
      dimension.spawnParticle("rza:purple_laser", pos);
      dimension.spawnParticle("rza:purple_laser_outward", pos);
      dimension.getEntities({ location: pos, families: ["zombie"], maxDistance: 3 }).forEach((zombie) => {
        zombie.applyDamage(7, { cause: EntityDamageCause.contact, damagingEntity: laserTurret });
        zombie.setOnFire(3, true);
      });
    } catch (e) {}
  }

  dimension.playSound("turret.laser_turret.fire", location, { volume: 8 });
  return;
}
