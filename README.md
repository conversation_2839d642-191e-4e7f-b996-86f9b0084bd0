<div align="center">

# 🧟‍♂️ **<PERSON><PERSON>'s Zombie Apocalypse** 🧟‍♀️

</div>

<div align="center">

Experience a complete transformation of Minecraft Bedrock in this expansive post-apocalyptic Add-On. Engage with evolved zombies and establish formidable defenses in a wasteland that challenges your survival instincts. This Bedrock Add-On combines strategic base building, advanced automation, and intense combat mechanics to deliver an immersive apocalyptic experience.

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Minecraft Version](https://img.shields.io/badge/Minecraft-1.21.50+-green)
![Version](https://img.shields.io/badge/version-1.2.4-orange.svg)

---

## 🌟 Features

</div>

### 🎮 Gameplay Elements

-   **Defense Systems** 🛡️

    -   Repair Arrays for equipment maintenance
    -   Arrow Turrets for base defense
    -   **Storm Weavers** that harness lightning attacks ⚡
    -   **Pyro Chargers** that unleash fireballs 🔥
    -   Automated targeting systems with configurable options

-   **Zombie Evolution** 🧟‍♂️
    -   **Alpha Zombies** that empower nearby zombies
    -   **Mutated Zombies** with enhanced abilities
    -   Dynamic zombie behavior influenced by player actions

### 🌍 World Generation

-   **Custom Structures** 🏰

    -   **Illager Settlements** (105x102 blocks)
        -   Fortified civilation with defensive towers
        -   Populated by illagers, villagers, and arrow turrets
        -   Rich loot
    -   **Abandoned Houses**
        -   Randomized loot including saplings, turrets, and rare resources
        -   Atmospheric ruins scattered across the world

-   **Environmental Changes** 🌫️
    -   Unique fog settings creating an apocalyptic atmosphere
    -   Modified biome appearances with altered water colors
    -   Fallen tree variations (Oak, Birch, Spruce, Jungle) adding to the desolate landscape

### 🎯 Game Mechanics

-   **Enhanced AI** 🧠

    -   Modified Iron Golem behavior to avoid attacking weakened villagers and illagers
    -   Smart zombie targeting based on player interactions
    -   Advanced pathfinding systems for entities

-   **Loot System** 🪙
    -   Balanced structure loot distribution
    -   Custom drop rates for different enemies
    -   Rare item discoveries in abandoned structures
    -   Expanded loot tables with new items like saplings, turrets, and resources

---

## 🔧 Core Mechanics

### Turrets and Defense Systems 🛡️

-   **Repair Arrays**

    -   Automatically repair nearby turrets and player equipment
    -   Utilize cooldown mechanisms to balance gameplay
    -   Coverage of up to 32 units with priority targeting

-   **Storm Weavers and Pyro Chargers** ⚡🔥

    -   Turrets that harness elemental powers
    -   **Storm Weavers** trigger chain lightning attacks against mobs
    -   **Pyro Chargers** launch explosive fireballs at enemies
    -   Configurable targeting options and behaviors through UI

-   **Witherators and Sonic Cannons** 💀🔊
    -   **Witherators** fire wither skulls at targets
    -   **Sonic Cannons** emit powerful blasts affecting multiple entities
    -   Strategic placement enhances base defense capabilities

### Zombie Mechanics 🧟‍♂️

-   **Alpha Zombies** 💪

    -   Emit a buff that enhances nearby zombies within 8 blocks
    -   Can mutate regular zombies into more powerful forms
    -   Central role in escalating the difficulty over time

-   **Mutation System** 🔬

    -   Zombies can mutate based on world conditions and alpha influence
    -   Mutated zombies possess increased stats and unique abilities
    -   Dynamic scaling to match player progression and challenge level

-   **Behavioral AI** 🧠
    -   Feral zombies capable of leaping attacks
    -   Walkers and other variants with specialized behaviors
    -   Enhanced targeting logic to create a more immersive experience

### Project Structure 📁

<details>
    <summary>📦 Items</summary>

### **Arrow Turret Item** 🏹🎯

-   _Description_: A defensive turret that shoots arrows at hostile mobs.
-   _Usage_: Place on a Turret Base to activate.

### **Pyro Charger Item** 🔥💥

-   _Description_: A turret that launches fireballs at enemies.
-   _Usage_: Requires a Turret Base for deployment.

### **Sonic Cannon Item** 🎶🔊

-   _Description_: Emits powerful sonic blasts affecting multiple foes.
-   _Usage_: Set up on a Turret Base to defend areas.

### **Storm Weaver Item** ⚡🌩️

-   _Description_: Turret that harnesses lightning to strike enemies.
-   _Usage_: Activated when placed on a Turret Base.

### **Pulsar System Item** 💫🔄

-   _Description_: Converts unwanted items into useful resources.
-   _Usage_: Processes items within its pulse radius.

### **Repair Array Item** 🛠️🔧

-   _Description_: Repairs damaged turrets and player equipment nearby.
-   _Usage_: Place to maintain gear and defenses automatically.

### **Witherator Item** 💀⚔️

-   _Description_: Fires wither skulls at zombies.
-   _Usage_: Deployed on a Turret Base for advanced defense.

### **Pyro Charger Machine** 🔥🏭

-   _Description_: Pre-requisite component for crafting the Pyro Charger turret.
-   _Usage_: Crafted first before assembling the final Pyro Charger.

### **Sonic Compressor** 🔊🔄

-   _Description_: Core component required to build the Sonic Cannon turret.
-   _Usage_: Needed to craft the final Sonic Cannon.

### **Electron Magnifier** ⚡🔍

-   _Description_: A vital part for constructing the Storm Weaver turret.
-   _Usage_: Used in crafting to enhance electrical energy focus.

### **Electron Channeler** 🔌🌐

-   _Description_: Component that channels electrical energy for turrets.
-   _Usage_: Required in crafting the Storm Weaver.

### **Emerald Rod** 💎🔗

-   _Description_: A specialized rod used in advanced turret crafting.
-   _Usage_: Part of the crafting recipe for the Repair Array.

### **Emerald Controller** 💎🕹️

-   _Description_: Controls energy flow within certain turrets.
-   _Usage_: Used when crafting the Repair Array.

### **Energy Director** ⚡📈

-   _Description_: Directs and regulates energy in turret systems.
-   _Usage_: Component in assembling the Repair Array.

### **Wither Core** 💀🔋

-   _Description_: Essential component for creating the Witherator turret.
-   _Usage_: Harnesses wither energy for powerful attacks.

### **Electron Reactor Core** ⚡🌀

-   _Description_: Initial form of the reactor core needed for the Storm Weaver.
-   _Usage_: Must be activated by lightning to become usable in crafting.

### **Active Electron Reactor Core** ⚡🔥

-   _Description_: Activated reactor core for advanced crafting.
-   _Usage_: Used to craft the Electron Magnifier after being struck by lightning.

</details>

<details>
    <summary>👾 Entities</summary>

## **Utility**

## **Zombies**

#### **Alpha Zombie** 🧟‍♂️💪

-   _Description_: An evolved zombie that empowers others.
-   _Abilities_: Buffs nearby zombies and induces mutations.
-   _Behavior_: Central to increasing game difficulty over time.

#### **Feral Zombie** 🧟‍♂️⚡

-   _Description_: A swift zombie capable of leaping attacks.
-   _Abilities_: Can leap towards players to close gaps quickly.

#### **Walker Zombie** 🧟‍♂️🚶‍♂️

-   _Description_: The most common zombie variant serving as the base for others.
-   _Abilities_: Enhanced health and damage compared to standard zombies.
-   _Features_:
    -   Improved pathfinding to track players more effectively.
    -   Custom animations for a more immersive experience.

#### **Miner Zombie** 🧟‍♂️⛏️

-   _Description_: A zombie that utilizes explosives against players.
-   _Abilities_: Throws TNT to cause area damage.
-   _Features_:
    -   Capable of environmental destruction.
    -   Higher durability and resistance.

#### **Spitter Zombie** 🧟‍♂️💧

-   _Description_: A ranged attacker that spits acid at targets.
-   _Abilities_: Fires acidic projectiles creating damaging puddles.
-   _Features_:
    -   Acid pools inflict ongoing damage.
    -   Effective at weakening players from a distance.

#### **Zombie Villager** 🧟‍♀️🏡

-   _Description_: Infected villagers that have turned into zombies.
-   _Abilities_: Behaves like a zombie but may retain villager traits.
-   _Features_:
    -   Can be cured back into villagers.
    -   May drop unique items upon defeat.

#### **Zombie Evoker** 🧟‍♂️🔮

-   _Description_: A zombified illager retaining magical abilities.
-   _Abilities_: Summons evocation fangs to attack.
-   _Features_:
    -   Uses spell-like attacks.
    -   Significant threat due to magic.

#### **Zombie Pillager** 🧟‍♂️🏹

-   _Description_: A zombified pillager wielding ranged weapons.
-   _Abilities_: Attacks with crossbows from a distance.
-   _Features_:
    -   Combines zombie resilience with ranged attacks.
    -   Enhanced stats over standard pillagers.

#### **Zombie Vindicator** 🧟‍♂️🛠️

-   _Description_: A zombified vindicator with strong melee attacks.
-   _Abilities_: Wields axes dealing high damage.
-   _Features_:
    -   Aggressive pursuit of players.
    -   Powerful in close combat.

#### **Mutated Zombie** 🧟‍♂️🔬

-   _Description_: A stronger variant of the standard zombie that appears starting day 60.
-   _Abilities_: Enhanced stats and unique combat abilities.
-   _Origins_: Created through random mutation chance or Alpha Zombie influence.
-   _Variants_:
    -   **Mutated Walker** 🚶‍♂️⚡: Faster movement and higher health.
    -   **Mutated Miner** ⛏️💣: TNT throwing capability and increased durability.
    -   **Mutated Feral** 🧟‍♂️🏃‍♂️: High-jump attacks and headbutt ability.
    -   **Mutated Spitter** 🧟‍♂️💧🔮: Advanced ranged attacks with acidic projectiles that create larger acid puddles.
    -   **Mutated Alpha** 🧟‍♂️🧬: Increased health (160), faster movement when attacking (170% speed), enhanced damage (12), and more potent buffs for nearby zombies.
-   _Stats_:
    -   **Attack Range**: 12 blocks (normal), 24 blocks (mutated)
    -   **Acid Puddle Radius**: 3 blocks (normal), 5 blocks (mutated)
    -   **Acid Damage**: 2 per tick + Poison I (normal), 4 per tick + Poison II (mutated)
-   _Features_:
    -   Distinctive red glowing eyes.
    -   Increased XP drops.
    -   Enhanced combat statistics.
-   _Spawn Chances_:
    -   **Easy**: 10% (25% during full moon)
    -   **Normal**: 20% (40% during full moon)
    -   **Hard**: 40% (60% during full moon)

## **Turrets**

#### **Arrow Turret** 🏹🔫

-   _Description_: A defensive turret that shoots arrows at hostile mobs.
-   _Behavior_:
    -   Automatically targets and fires arrows at enemies within a 24-block radius.
    -   Rotates smoothly to track and engage multiple targets.
-   _Mechanics_:
    -   Configurable targeting priority through the turret interface.
    -   Rate of fire balanced to provide effective defense without overuse.
-   _Features_:
    -   Can be upgraded with different arrow types for varied damage effects.
    -   Visual and audio cues indicate targeting and firing states.
-   _Stats_:
    -   **Effective Range**: 24 blocks
    -   **Damage**: 10 per arrow
    -   **Rate of Fire**: 1 arrow per second

#### **Pyro Charger** 🔥🔋

-   _Description_: A turret that launches explosive fireballs at hostile entities.
-   _Behavior_:
    -   Automatically detects and targets enemies within a 24-block radius.
    -   Fires fireballs that cause area damage and can ignite targets upon impact.
-   _Mechanics_:
    -   Includes an overheating system; continuous firing leads to overheating, triggering a cooldown period.
    -   Configurable targeting options to prioritize specific enemy types.
    -   Operates with a cooldown to balance its powerful attacks.
-   _Features_:
    -   Visual indicators display overheating and cooldown status.
    -   Can be integrated with other defense systems for enhanced base protection.
-   _Stats_:
    -   **Effective Range**: 24 blocks
    -   **Damage**: 15 (direct hit) plus fire damage over time
    -   **Overheating Threshold**: Fires 10 shots before overheating
    -   **Cooldown Time**: 15 seconds

#### **Repair Array** 🛠️🛡️

-   _Description_: A defensive turret that autonomously repairs nearby turrets and player equipment.
-   _Behavior_:
    -   Monitors a 32-block radius for damaged turrets and player gear.
    -   Initiates automatic repairs on detected damage.
    -   Operates only when placed on a turret base.
-   _Mechanics_:
    -   Implements a cooldown system, allowing repairs every 2 seconds.
    -   Repairs up to 5 entities simultaneously.
    -   Differentiates repair amounts:
        -   Restores 4 damage points for turrets.
        -   Restores 2 damage points per piece for player equipment.
    -   Prevents self-repair.
-   _Features_:
    -   Visual indicators display active repairs and cooldown status.
    -   Integration with other defense systems.
    -   Configurable settings through the Repair Array interface.
-   _Stats_:
    -   **Detection Range**: 32 blocks
    -   **Firing Range**: 32 blocks
    -   **Rate of Fire**: Every 2 seconds
    -   **Repair Capacity**: Repairs up to 5 turrets and player equipment
    -   **Repair Amount**:
        -   **Turrets**: 4 damage points per repair cycle
        -   **Player Equipment**: 2 damage points per piece per repair cycle

#### **Sonic Cannon** 🎶🔊

-   _Description_: Emits powerful sonic pulses damaging multiple foes.
-   _Behavior_:
    -   Fires concentrated sonic waves that pierce through multiple targets.
    -   Effective against groups due to its line-of-effect damage.
-   _Mechanics_:
    -   Requires a charging period before each shot, indicated by visual and audio cues.
    -   Cannot fire continuously; must recharge between pulses.
    -   Configurable to target specific enemy types or prioritize threats.
-   _Features_:
    -   Long-range capabilities with a maximum effective range of 48 blocks.
    -   User interface allows for customization of targeting preferences.
    -   Visual effects showcase charging and firing sequences.
-   _Stats_:
    -   **Effective Range**: 48 blocks
    -   **Damage**: 20 per target hit
    -   **Charge Time**: 5 seconds
    -   **Cooldown Time**: 3 seconds

#### **Storm Weaver** 🌩️⚡

-   _Description_: Strikes enemies with chain lightning attacks.
-   _Behavior_:
    -   Targets enemies and calls down lightning bolts to strike them.
    -   Lightning can chain to nearby enemies within a certain radius.
-   _Mechanics_:
    -   Chance for lightning to propagate to additional targets (chain lightning effect).
    -   More effective during storms, leveraging weather conditions.
    -   Has a recharge period between strikes to prevent overuse.
-   _Features_:
    -   Configurable settings to prioritize enemy types.
    -   Visual indicators for readiness and cooldown periods.
    -   Can cause extra damage to specific enemy classes, like zombies.
-   _Stats_:
    -   **Effective Range**: 32 blocks
    -   **Damage**: 10 per lightning strike
    -   **Chain Lightning Radius**: 5 blocks
    -   **Cooldown Time**: 7 seconds

#### **Pulsar System** 🌟🔄

-   _Description_: A versatile turret system that converts unwanted items into useful resources.
-   _Behavior_:
    -   Continuously scans the surrounding area for items to process within its pulse radius.
    -   Generates resource pulses that convert collected items into predefined resources.
-   _Mechanics_:
    -   Configurable pulse radius to control the area of effect.
    -   Automatically manages item conversion based on priority settings.
    -   Utilizes energy efficiently to optimize resource conversion rates.
-   _Features_:
    -   Integrated with other systems for seamless item collection and conversion.
    -   Visual indicators display active pulses and conversion status.
    -   Customizable settings through the Pulsar System interface for tailored operations.
-   _Stats_:
    -   **Effective Pulse Radius**: 16 blocks
    -   **Conversion Rate**: 5 items per pulse
    -   **Energy Consumption**: 20 units per pulse
    -   **Cooldown Time**: 2 seconds between pulses

#### **Witherator** 💀🔥

-   _Description_: Fires wither skulls that inflict damage and apply wither effects to zombies.
-   _Behavior_:
    -   Automatically targets and launches wither skulls at zombies within its range.
    -   Applies the wither effect, causing damage over time and weakening affected enemies.
-   _Mechanics_:
    -   Balances firing rate to provide consistent damage without rapid depletion of ammo.
    -   Incorporates an overheating system; continuous firing leads to overheating, triggering a cooldown period.
    -   Configurable targeting options to prioritize specific zombie types or threat levels.
-   _Features_:
    -   Visual indicators display overheating and cooldown statuses for effective management.
    -   Can be integrated with other defense systems for enhanced base protection.
    -   Supports ammo upgrades to increase firing rate or damage output.
-   _Stats_:
    -   **Effective Range**: 24 blocks
    -   **Damage**: 12 per wither skull
    -   **Wither Effect Duration**: 5 seconds
    -   **Overheating Threshold**: Fires 15 shots before overheating
    -   **Cooldown Time**: 20 seconds

</details>

<details>
    <summary>🧱 Blocks</summary>

-   **Turret Base** 🏰

    -   _Description_: The foundational block required for deploying all turret types.
    -   _Functionality_:
        -   Serves as the primary mounting point for turrets.
        -   Integrates with power sources and control systems.
    -   _Design Enhancements_:
        -   **Visual Indicators**: Glowing edges to indicate active status.
        -   **Upgrade Slots**: Allows for modular enhancements like increased range or damage.
        -   **Durability**: Enhanced resistance against explosions and environmental damage.

-   **Electron Reactor Core** ⚡

    -   _Description_: A critical power source for Storm Weaver turrets.
    -   _Functionality_:
        -   Must be activated by lightning strikes to become operational.
        -   Supplies energy to multiple connected turrets.
    -   _Features_:
        -   **Inactive State**:
            -   No power output.
            -   Visible storm energy particles.
        -   **Activation Process**:
            -   Transforms into Active Electron Reactor Core upon lightning strike.
            -   Emits a surge of electrical energy upon activation.
        -   **Crafting Requirements**:
            -   Essential for assembling advanced Storm Weaver components.
        -   **Design Enhancements**:
            -   **Interactive UI**: Displays real-time energy levels and status.
            -   **Cooling System**: Prevents overheating during prolonged use.

-   **Active Electron Reactor Core** 🔋
    -   _Description_: The energized form of the Electron Reactor Core, essential for advanced crafting.
    -   _Functionality_:
        -   Powers high-tier Storm Weaver turrets and related components.
        -   Provides sustained energy output for extended operations.
    -   _Features_:
        -   **Light Emission**: Emits light level 5, illuminating surrounding areas.
        -   **Particle Effects**: Dynamic electrical particles enhance visual appeal.
        -   **Explosion Resistance**: High resilience with a resistance rating of 200.
        -   **Crafting Uses**:
            -   Utilized in crafting Electron Magnifiers and other advanced devices.
        -   **Design Enhancements**:
            -   **Energy Pulse Animation**: Visual pulses indicating energy flow.
            -   **Integration Points**: Multiple connectors for seamless integration with other blocks.

</details>

<details>
    <summary>🏢 Structures</summary>

-   **Fallen Trees** 🌲

    -   _Description_: Decorative blocks that enhance the post-apocalyptic aesthetic of the environment.
    -   _Types_: Available in Oak, Birch, Spruce, and Jungle variations.
    -   _Features_:
        -   **Environmental Impact**: Adds depth and realism to the landscape.
        -   **Interactive Elements**: Players can gather materials from fallen trees.
        -   **Design Enhancements**:
            -   **Decay States**: Different stages of decay for a more natural look.
            -   **Dynamic Placement**: Randomized positioning to create varied terrains.

-   **Abandoned Structures** 🏚️
    -   _Description_: Lootable buildings strategically scattered throughout the world.
    -   _Contents_:
        -   **Loot Drops**: Includes saplings, turrets, rare resources, and exclusive items.
        -   **Security Systems**: Some structures contain hidden defenses or traps.
        -   **Design Enhancements**:
            -   **Varied Architecture**: Different building styles to add diversity.
            -   **Interactive Interiors**: Players can explore and interact with various elements inside.
            -   **Spawn Points**: Potential spawn points for hostile entities to increase challenge.

</details>

## 🛠️ Technical Details

### Dependencies 📦

-   `@minecraft/server` module for core scripting API
-   `@minecraft/server-ui` module for user interface elements
-   TypeScript for type safety and cleaner code structure
-   `@minecraft/vanilla-data/lib` for accurate Minecraft identifiers
-   `Raboy's Zombie Apocalypse Worldgen Pack` pack required to spawn custom structures naturally

## 📦 Installation

1. Download the latest release
2. Import the behavior pack into Minecraft
3. Enable the pack in your world settings
4. Start a new world or explore unexplored chunks

## 🔧 Requirements

-   Minecraft Bedrock Edition 1.21.40 or higher
-   Experimental Features: Custom Biomes Required

<div align="center">

## 🤝 Contributing

This project is protected under copyright. Modifications are only permitted for personal use.

## ⚖️ License

© 2024 Raboy13. All rights reserved.

<div align="left">

### Usage Terms:

-   No public modifications
-   No unauthorized redistribution
-   Credit required for content creation
-   Personal use modifications allowed

</div>

---

# 👥 Credits

### <span style="font-family: 'Minecraft', sans-serif;">**Created by:** Raboy13</span>

[![YouTube](https://img.shields.io/badge/YouTube-Subscribe%20-FF0000?style=for-the-badge&logo=youtube&logoColor=white)](https://youtube.com/raboy13)

[![Patreon](https://img.shields.io/badge/Patreon-Support%20Me-orange?style=for-the-badge&logo=patreon&logoColor=white)](https://www.patreon.com/c/Raboy13)

---

## 📬 Contact

For permissions and inquiries:  
[![Discord](https://img.shields.io/badge/Discord-7289DA?style=for-the-badge&logo=discord&logoColor=white)](https://discord.gg/yxbDH2YFbb)

# Made with 💜 by Raboy13

</div>
