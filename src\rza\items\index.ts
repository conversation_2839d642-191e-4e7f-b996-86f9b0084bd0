import { world, ItemCustomComponent, ItemComponentUseEvent, system } from "@minecraft/server";
import { itemFeatures } from "./items";

/**
 * Custom item component for handling item usage behavior
 * Implements the new ItemCustomComponent interface
 */
class RzaItemComponent implements ItemCustomComponent {
  /**
   * Called when the item is used by a player
   * @param event The use event data
   * @param params Custom component parameters (unused in this implementation)
   */
  onUse(event: ItemComponentUseEvent): void {
    const item = event.itemStack;
    const player = event.source;
    if (!item) return;

    const itemTypeId = item.type.id;
    itemFeatures(item, itemTypeId, player);
  }
}

/**
 * Registers all custom item components for the addon using the new system
 * Uses world.beforeEvents.worldInitialize for proper registration timing
 */
system.beforeEvents.startup.subscribe((initEvent) => {
  // Register the item component using the new class-based approach
  initEvent.itemComponentRegistry.registerCustomComponent("rza:item", new RzaItemComponent());
});

// Subscribe to item use events for continuous use handling
world.beforeEvents.itemUse.subscribe((data) => {
  const item = data.itemStack;
  const player = data.source;
  if (!item) return;

  itemFeatures(item, item.typeId, player, true);
});

world.afterEvents.itemStopUse.subscribe((data) => {
  const item = data.itemStack;
  const player = data.source;
  if (!item) return;

  itemFeatures(item, item.typeId, player, false);
});
