import { Dimension, Entity, Vector3 } from "@minecraft/server";

/**
 * Transforms a vanilla mob into a basic walker zombie
 * @param entity The source entity to be transformed into a walker zombie
 * @param dimension The Minecraft dimension where the transformation will occur
 * @param location The precise Vector3 location where the walker zombie will be spawned
 * @remarks Handles the conversion of vanilla mobs like zombies, skeletons, and creepers into RZA walker zombies
 * @throws Will spawn a default walker if transformation encounters an error
 */
export function transformToWalker(entity: Entity, dimension: Dimension, location: Vector3) {
  try {
    const walker = dimension.spawnEntity("rza:walker" as any, location);
    entity.remove();
    walker.triggerEvent("minecraft:entity_transformed");
  } catch (error) {
    dimension.spawnEntity("rza:walker" as any, location);
  }
}

/**
 * Transforms a witch into a feral zombie
 * @param entity The witch entity to transform
 * @param dimension The dimension to spawn the feral zombie in
 * @param location The location to spawn the feral zombie at
 * @remarks Handles the transformation of a witch entity into a feral zombie, removing the original entity
 */
export function transformToFeral(entity: <PERSON>ti<PERSON>, dimension: Dimension, location: Vector3) {
  try {
    const feral = dimension.spawnEntity("rza:feral" as any, location);
    entity.remove();
    feral.triggerEvent("minecraft:entity_transformed");
  } catch (error) {
    dimension.spawnEntity("rza:feral" as any, location);
  }
}
