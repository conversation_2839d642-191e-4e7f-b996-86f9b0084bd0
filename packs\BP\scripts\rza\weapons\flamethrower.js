import { ItemStack, system, EntityComponentTypes, ItemComponentTypes, EquipmentSlot } from "@minecraft/server";
import { getRandomFloat } from "../utils/rng";
const FLAMETHROWER_ITEM = "rza:flamethrower";
const FIREBALL_PROJECTILE = "rza:flamethrower_fireball";
const PROJECTILE_OFFSET = 1;
const PROJECTILE_SPEED = 1.0;
const CONTINUOUS_FIRE_INTERVAL = 1;
const FIRE_CHARGE_ITEM = "minecraft:fire_charge";
let activeFlamethrowers = new Map();
function getColorForPercentage(percentage) {
    if (percentage > 75)
        return "§a";
    if (percentage > 50)
        return "§e";
    if (percentage > 25)
        return "§6";
    return "§c";
}
function formatDurabilityText(damage, max) {
    const remaining = Math.max(0, max - damage);
    const percentage = (remaining / max) * 100;
    const color = getColorForPercentage(percentage);
    return { actionBar: `§6Fuel: ${color}${remaining} | §7${color}${max}`, lore: `${color}Fuel: ${remaining} | ${max}` };
}
function updateDurabilityDisplay(player, current, max, itemStack) {
    const formatted = formatDurabilityText(current, max);
    player.onScreenDisplay.setActionBar(formatted.actionBar);
    if (itemStack) {
        try {
            const lore = [formatted.lore];
            itemStack.setLore(lore);
        }
        catch (error) {
            console.warn(`Failed to update lore for player ${player.name}: ${error}`);
        }
    }
    return;
}
function consumeFireCharge(player) {
    try {
        const inventory = player.getComponent(EntityComponentTypes.Inventory)?.container;
        if (!inventory)
            return false;
        for (let i = 0; i < inventory.size; i++) {
            const slot = inventory.getSlot(i);
            const item = slot.getItem();
            if (item?.typeId === FIRE_CHARGE_ITEM) {
                if (item.amount > 1) {
                    slot.setItem(new ItemStack(item.type.id, item.amount - 1));
                }
                else {
                    slot.setItem(undefined);
                }
                return true;
            }
        }
    }
    catch (error) {
        console.warn(`Failed to consume fire charge for player ${player.name}: ${error}`);
    }
    return false;
}
function handleOutOfFuel(player, itemStack) {
    try {
        const durabilityComponent = itemStack.getComponent(ItemComponentTypes.Durability);
        if (!durabilityComponent)
            return;
        const refueled = consumeFireCharge(player);
        if (refueled) {
            durabilityComponent.damage = 0;
            updateDurabilityDisplay(player, 0, durabilityComponent.maxDurability, itemStack);
            const equipmentComponent = player.getComponent(EntityComponentTypes.Equippable);
            if (equipmentComponent) {
                equipmentComponent.setEquipment(EquipmentSlot.Mainhand, itemStack);
                player.onScreenDisplay.setActionBar("§aFlamethrower Refueled!");
                player.dimension.playSound("bucket.fill_lava", player.location, { volume: 0.1, pitch: 1.0 });
            }
        }
        else {
            const maxDurability = durabilityComponent.maxDurability;
            durabilityComponent.damage = maxDurability;
            updateDurabilityDisplay(player, maxDurability, maxDurability, itemStack);
            const equipmentComponent = player.getComponent(EntityComponentTypes.Equippable);
            if (equipmentComponent) {
                equipmentComponent.setEquipment(EquipmentSlot.Mainhand, itemStack);
            }
            player.onScreenDisplay.setActionBar("§cOut of Fuel! Need Fire Charge!");
            player.dimension.playSound("mob.blaze.shoot", player.location, { volume: 0.3, pitch: 0.5 });
        }
    }
    catch (error) {
        console.warn(`Failed to handle out of fuel for player ${player.name}: ${error}`);
    }
    return;
}
export function handleFlamethrower(itemStack, player, startUsing) {
    if (itemStack.type.id !== FLAMETHROWER_ITEM)
        return;
    if (startUsing !== undefined) {
        const current = activeFlamethrowers.get(player.id);
        if (startUsing) {
            if (current) {
                stopFlamethrower(player);
            }
            const durabilityComponent = itemStack.getComponent(ItemComponentTypes.Durability);
            if (!durabilityComponent)
                return;
            fireProjectile(itemStack, player);
            const intervalId = system.runInterval(() => {
                if (!player) {
                    stopFlamethrower(player);
                    return;
                }
                const heldItem = player.getComponent(EntityComponentTypes.Equippable)?.getEquipment(EquipmentSlot.Mainhand);
                if (!heldItem || heldItem.typeId !== FLAMETHROWER_ITEM) {
                    stopFlamethrower(player);
                    return;
                }
                const cooldownComponent = heldItem.getComponent(ItemComponentTypes.Cooldown);
                if (cooldownComponent?.getCooldownTicksRemaining(player) > 0) {
                    return;
                }
                const flamethrowerState = activeFlamethrowers.get(player.id);
                if (!flamethrowerState || !flamethrowerState.isUsing) {
                    stopFlamethrower(player);
                    return;
                }
                if (fireProjectile(itemStack, player)) {
                    if (flamethrowerState) {
                        flamethrowerState.accumulatedDamage++;
                        const currentDamage = Math.min(Math.max(flamethrowerState.initialDurability + flamethrowerState.accumulatedDamage, 0), durabilityComponent.maxDurability);
                        updateDurabilityDisplay(player, currentDamage, durabilityComponent.maxDurability, itemStack);
                        if (currentDamage >= durabilityComponent.maxDurability) {
                            updateDurabilityDisplay(player, durabilityComponent.maxDurability, durabilityComponent.maxDurability, itemStack);
                            handleOutOfFuel(player, itemStack);
                            stopFlamethrower(player);
                            return;
                        }
                    }
                }
            }, CONTINUOUS_FIRE_INTERVAL);
            updateDurabilityDisplay(player, durabilityComponent.damage, durabilityComponent.maxDurability, itemStack);
            activeFlamethrowers.set(player.id, { intervalId, isUsing: true, accumulatedDamage: 0, initialDurability: durabilityComponent.damage });
        }
        else {
            stopFlamethrower(player);
        }
        return;
    }
    return;
}
function stopFlamethrower(player) {
    const current = activeFlamethrowers.get(player.id);
    if (!current)
        return;
    if (current.intervalId) {
        system.clearRun(current.intervalId);
    }
    current.isUsing = false;
    const equipmentComponent = player.getComponent(EntityComponentTypes.Equippable);
    if (!equipmentComponent) {
        activeFlamethrowers.delete(player.id);
        return;
    }
    const heldItem = equipmentComponent.getEquipment(EquipmentSlot.Mainhand);
    if (heldItem?.typeId === FLAMETHROWER_ITEM) {
        const durabilityComponent = heldItem.getComponent(ItemComponentTypes.Durability);
        if (durabilityComponent) {
            const newDamage = Math.min(Math.max(current.initialDurability + current.accumulatedDamage, 0), durabilityComponent.maxDurability);
            if (newDamage >= durabilityComponent.maxDurability) {
                handleOutOfFuel(player, heldItem);
            }
            else {
                try {
                    durabilityComponent.damage = newDamage;
                    updateDurabilityDisplay(player, newDamage, durabilityComponent.maxDurability, heldItem);
                    equipmentComponent.setEquipment(EquipmentSlot.Mainhand, heldItem);
                }
                catch (error) {
                    console.warn(`Failed to update flamethrower durability for player ${player.name}: ${error}`);
                }
            }
        }
    }
    activeFlamethrowers.delete(player.id);
    return;
}
function fireProjectile(itemStack, player) {
    const dimension = player.dimension;
    const viewDirection = player.getViewDirection();
    const headLocation = player.getHeadLocation();
    const checkPoints = [1, 2, 3].map((multiplier) => ({
        x: headLocation.x + viewDirection.x * multiplier,
        y: headLocation.y + viewDirection.y * multiplier,
        z: headLocation.z + viewDirection.z * multiplier
    }));
    const allPointsValid = checkPoints.every((point) => {
        const block = dimension.getBlock(point);
        return block && (block.isAir || block.hasTag("passable"));
    });
    if (!allPointsValid) {
        player.onScreenDisplay.setActionBar("§cPath blocked! Cannot fire in this direction!");
        return false;
    }
    const durabilityComponent = itemStack.getComponent(ItemComponentTypes.Durability);
    if (!durabilityComponent || durabilityComponent.damage >= durabilityComponent.maxDurability) {
        handleOutOfFuel(player, itemStack);
        return false;
    }
    const rightVector = { x: -viewDirection.z, y: 0, z: viewDirection.x };
    const spawnPos = {
        x: headLocation.x + viewDirection.x * PROJECTILE_OFFSET + rightVector.x * 0.2,
        y: headLocation.y + viewDirection.y * PROJECTILE_OFFSET,
        z: headLocation.z + viewDirection.z * PROJECTILE_OFFSET + rightVector.z * 0.2
    };
    const fireball = dimension.spawnEntity(FIREBALL_PROJECTILE, spawnPos);
    const projectileComponent = fireball.getComponent(EntityComponentTypes.Projectile);
    const horizontalSpreadFactor = 0.15;
    const verticalSpreadFactor = 0.05;
    const velocity = {
        x: viewDirection.x + getRandomFloat(-horizontalSpreadFactor, horizontalSpreadFactor, 3),
        y: viewDirection.y + getRandomFloat(-verticalSpreadFactor, verticalSpreadFactor, 3),
        z: viewDirection.z + getRandomFloat(-horizontalSpreadFactor, horizontalSpreadFactor, 3)
    };
    if (fireball) {
        if (projectileComponent) {
            projectileComponent.shoot({ x: velocity.x * PROJECTILE_SPEED, y: velocity.y * PROJECTILE_SPEED, z: velocity.z * PROJECTILE_SPEED });
            dimension.playSound("mob.blaze.shoot", spawnPos, { volume: 0.1 });
        }
    }
    player.startItemCooldown("flamethrower", 2);
    return true;
}
export function flamethrowerFireball(flamethrowerFireball) {
    try {
        if (flamethrowerFireball && flamethrowerFireball) {
            flamethrowerFireball.dimension
                .getEntities({ families: ["monster"], location: flamethrowerFireball.location, maxDistance: 3 })
                .forEach((zombie) => {
                zombie.applyDamage(1);
                zombie.setOnFire(60, true);
            });
        }
    }
    catch (error) { }
    return;
}
export function cleanupFlamethrowerData(playerId) {
    const current = activeFlamethrowers.get(playerId);
    if (current?.intervalId) {
        system.clearRun(current.intervalId);
    }
    activeFlamethrowers.delete(playerId);
    return;
}
