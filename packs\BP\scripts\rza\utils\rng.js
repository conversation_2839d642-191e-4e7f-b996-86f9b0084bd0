let generatedNumbers = [];
export function getRandomInt(min, max) {
    function isRangeExhausted() {
        return generatedNumbers.length >= max - min + 1;
    }
    min = Math.ceil(min);
    max = Math.floor(max);
    if (min > max) {
        console.warn(`getRandomInt: min (${min}) is greater than max (${max})`);
    }
    if (isRangeExhausted()) {
        generatedNumbers.length = 0;
    }
    let randomNum;
    do {
        randomNum = Math.floor(Math.random() * (max - min + 1)) + min;
    } while (generatedNumbers.includes(randomNum));
    generatedNumbers.push(randomNum);
    return randomNum;
}
export function getRandomFloat(min, max, decimals) {
    if (min > max) {
        console.warn(`getRandomFloat: min (${min}) is greater than max (${max})`);
    }
    const randomFloat = Math.random() * (max - min) + min;
    return Number(randomFloat.toFixed(decimals));
}
