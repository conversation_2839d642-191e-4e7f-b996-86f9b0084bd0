{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.spitter.normal", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 7, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "spine", "parent": "root", "pivot": [0, 12, 0]}, {"name": "head", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "jaw", "parent": "head", "pivot": [0, 16, -6.5], "cubes": [{"origin": [-3, 21, -5.5], "size": [6, 3, 3], "uv": [35, 0]}, {"origin": [-3, 24, -5.5], "size": [6, 1, 0], "uv": {"north": {"uv": [51, 0], "uv_size": [6, 1]}, "east": {"uv": [51, 0], "uv_size": [0, 1]}, "south": {"uv": [57, 0], "uv_size": [6, 1]}, "west": {"uv": [57, 0], "uv_size": [0, 1]}, "up": {"uv": [51, 0], "uv_size": [6, 0]}, "down": {"uv": [57, 0], "uv_size": [6, 0]}}}, {"origin": [3, 24, -5.5], "size": [0, 1, 0.75], "uv": {"north": {"uv": [51, 2], "uv_size": [0, 1]}, "east": {"uv": [52, 2], "uv_size": [-1, 1]}, "south": {"uv": [52, 2], "uv_size": [0, 1]}, "west": {"uv": [51, 2], "uv_size": [-1, 1]}, "up": {"uv": [51, 1], "uv_size": [0, 1]}, "down": {"uv": [51, 2], "uv_size": [0, -1]}}}, {"origin": [3, 24, -4.75], "size": [0, 1, 0.75], "uv": {"north": {"uv": [53, 2], "uv_size": [0, 1]}, "east": {"uv": [54, 2], "uv_size": [-1, 1]}, "south": {"uv": [54, 2], "uv_size": [0, 1]}, "west": {"uv": [53, 2], "uv_size": [-1, 1]}, "up": {"uv": [53, 1], "uv_size": [0, 1]}, "down": {"uv": [53, 2], "uv_size": [0, -1]}}}, {"origin": [-3, 24, -5.5], "size": [0, 1, 0.75], "uv": {"north": {"uv": [55, 2], "uv_size": [0, 1]}, "east": {"uv": [54, 2], "uv_size": [1, 1]}, "south": {"uv": [56, 2], "uv_size": [0, 1]}, "west": {"uv": [55, 2], "uv_size": [1, 1]}, "up": {"uv": [55, 1], "uv_size": [0, 1]}, "down": {"uv": [55, 2], "uv_size": [0, -1]}}}, {"origin": [-3, 24, -4.75], "size": [0, 1, 0.75], "uv": {"north": {"uv": [57, 2], "uv_size": [0, 1]}, "east": {"uv": [56, 2], "uv_size": [1, 1]}, "south": {"uv": [58, 2], "uv_size": [0, 1]}, "west": {"uv": [57, 2], "uv_size": [1, 1]}, "up": {"uv": [57, 1], "uv_size": [0, 1]}, "down": {"uv": [57, 2], "uv_size": [0, -1]}}}, {"origin": [-3, 22, -2.5], "size": [6, 2, 0.5], "uv": {"north": {"uv": [24, 0], "uv_size": [6, 2]}, "east": {"uv": [30, 2], "uv_size": [1, 2]}, "south": {"uv": [30, 0], "uv_size": [6, 2]}, "west": {"uv": [30, 2], "uv_size": [-1, 2]}, "up": {"uv": [24, 0], "uv_size": [6, 0]}, "down": {"uv": [31, 3], "uv_size": [4, -1]}}}]}, {"name": "body", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}], "locators": {"blood": {"offset": [0, 23, 0], "rotation": [-90, 0, 0]}}}, {"name": "leftArm", "parent": "spine", "pivot": [5, 22, 0], "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [32, 48]}]}, {"name": "leftItem", "parent": "leftArm", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "spine", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1], "locators": {"lead_hold": [-6, 15, 1]}}, {"name": "waist", "parent": "root", "pivot": [0, 12, 0]}, {"name": "left_leg", "parent": "waist", "pivot": [1.9, 12, 0], "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 12, 4], "uv": [16, 48]}]}, {"name": "right_leg", "parent": "waist", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}]}, {"description": {"identifier": "geometry.spitter.normal.death", "texture_width": 64, "texture_height": 64, "visible_bounds_width": 7, "visible_bounds_height": 4, "visible_bounds_offset": [0, 1, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "leftArm", "parent": "root", "pivot": [5, 22, 0], "cubes": [{"origin": [4, 12, -2], "size": [4, 12, 4], "uv": [32, 48]}]}, {"name": "leftItem", "parent": "leftArm", "pivot": [6, 15, 1]}, {"name": "rightArm", "parent": "root", "pivot": [-5, 22, 0], "cubes": [{"origin": [-8, 12, -2], "size": [4, 12, 4], "uv": [40, 16]}]}, {"name": "rightItem", "parent": "rightArm", "pivot": [-6, 15, 1], "locators": {"lead_hold": [-6, 15, 1]}}, {"name": "spine", "parent": "root", "pivot": [0, 12, 0]}, {"name": "head", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 24, -4], "size": [8, 8, 8], "uv": [0, 0]}]}, {"name": "jaw", "parent": "head", "pivot": [0, 16, -6.5], "cubes": [{"origin": [-3, 21, -5.5], "size": [6, 3, 3], "uv": [35, 0]}, {"origin": [-3, 22, -2.5], "size": [6, 2, 0.5], "uv": {"north": {"uv": [24, 0], "uv_size": [6, 2]}, "east": {"uv": [24, 0], "uv_size": [0, 2]}, "south": {"uv": [30, 0], "uv_size": [6, 2]}, "west": {"uv": [30, 0], "uv_size": [0, 2]}, "up": {"uv": [24, 0], "uv_size": [6, 0]}, "down": {"uv": [30, 0], "uv_size": [6, 0]}}}]}, {"name": "body", "parent": "spine", "pivot": [0, 24, 0], "cubes": [{"origin": [-4, 12, -2], "size": [8, 12, 4], "uv": [16, 16]}], "locators": {"blood": {"offset": [0, 23, 0], "rotation": [-90, 0, 0]}}}, {"name": "waist", "parent": "root", "pivot": [0, 0, 0]}, {"name": "left_leg", "parent": "waist", "pivot": [1.9, 12, 0], "cubes": [{"origin": [-0.1, 0, -2], "size": [4, 12, 4], "uv": [16, 48]}]}, {"name": "right_leg", "parent": "waist", "pivot": [-1.9, 12, 0], "cubes": [{"origin": [-3.9, 0, -2], "size": [4, 12, 4], "uv": [0, 16]}]}]}]}