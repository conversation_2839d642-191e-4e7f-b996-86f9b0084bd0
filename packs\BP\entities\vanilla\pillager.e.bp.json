{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "minecraft:pillager", "is_spawnable": true, "is_summonable": true, "is_experimental": false}, "component_groups": {"rza:settler": {"minecraft:preferred_path": {"max_fall_blocks": 1, "jump_cost": 5, "default_block_cost": 1.5, "preferred_path_blocks": [{"cost": 0, "blocks": ["grass_path"]}, {"cost": 1, "blocks": ["cobblestone", "stone", "granite", "polished_granite", "diorite", "polished_diorite", "andesite", "polished_andesite", "stone_bricks", "mossy_stone_bricks", "cracked_stone_bricks", "chiseled_stone_bricks", "sandstone", "cut_sandstone", "chiseled_sandstone", "smooth_sandstone", "mossy_cobblestone", "smooth_stone_slab", "sandstone_slab", "cobblestone_slab", "brick_slab", "stone_brick_slab", "quartz_slab", "nether_brick_slab", "red_sandstone_slab", "purpur_slab", "prismarine_slab", "dark_prismarine_slab", "prismarine_brick_slab", "mossy_cobblestone_slab", "smooth_sandstone_slab", "red_nether_brick_slab", "end_stone_brick_slab", "smooth_red_sandstone_slab", "polished_andesite_slab", "andesite_slab", "diorite_slab", "polished_diorite_slab", "granite_slab", "polished_granite_slab", "mossy_stone_brick_slab", "smooth_quartz_slab", "normal_stone_slab", "cut_sandstone_slab", "cut_red_sandstone_slab", "smooth_stone_double_slab", "sandstone_double_slab", "cobblestone_double_slab", "brick_double_slab", "stone_brick_double_slab", "quartz_double_slab", "nether_brick_double_slab", "red_sandstone_double_slab", "purpur_double_slab", "prismarine_double_slab", "dark_prismarine_double_slab", "prismarine_brick_double_slab", "mossy_cobblestone_double_slab", "smooth_sandstone_double_slab", "red_nether_brick_double_slab", "end_stone_brick_double_slab", "smooth_red_sandstone_double_slab", "polished_andesite_double_slab", "andesite_double_slab", "diorite_double_slab", "polished_diorite_double_slab", "granite_double_slab", "polished_granite_double_slab", "mossy_stone_brick_double_slab", "smooth_quartz_double_slab", "normal_stone_double_slab", "cut_sandstone_double_slab", "cut_red_sandstone_double_slab", "oak_slab", "spruce_slab", "birch_slab", "jungle_slab", "acacia_slab", "dark_oak_slab", "oak_double_slab", "spruce_double_slab", "birch_double_slab", "jungle_double_slab", "acacia_double_slab", "dark_oak_double_slab", "oak_planks", "spruce_planks", "birch_planks", "jungle_planks", "acacia_planks", "dark_oak_planks", "brick_block", "nether_brick", "red_nether_brick", "end_bricks", "red_sandstone", "cut_red_sandstone", "chiseled_red_sandstone", "smooth_red_sandstone", "white_stained_glass", "orange_stained_glass", "magenta_stained_glass", "light_blue_stained_glass", "yellow_stained_glass", "lime_stained_glass", "pink_stained_glass", "gray_stained_glass", "light_gray_stained_glass", "cyan_stained_glass", "purple_stained_glass", "blue_stained_glass", "brown_stained_glass", "green_stained_glass", "red_stained_glass", "black_stained_glass", "glass", "glowstone", "prismarine", "emerald_block", "diamond_block", "lapis_block", "gold_block", "redstone_block", "purple_glazed_terracotta", "white_glazed_terracotta", "orange_glazed_terracotta", "magenta_glazed_terracotta", "light_blue_glazed_terracotta", "yellow_glazed_terracotta", "lime_glazed_terracotta", "pink_glazed_terracotta", "gray_glazed_terracotta", "silver_glazed_terracotta", "cyan_glazed_terracotta", "blue_glazed_terracotta", "brown_glazed_terracotta", "green_glazed_terracotta", "red_glazed_terracotta", "black_glazed_terracotta"]}, {"cost": 50, "blocks": ["bed", "lectern", "composter", "grindstone", "blast_furnace", "smoker", "fletching_table", "cartography_table", "brewing_stand", "smithing_table", "cauldron", "barrel", "loom", "stonecutter"]}]}, "minecraft:behavior.defend_village_target": {"priority": 0, "attack_chance": 0.75, "entity_types": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "value": "player"}]}}}, "minecraft:behavior.move_towards_dwelling_restriction": {"priority": 1, "speed_multiplier": 1.1}, "minecraft:dweller": {"dwelling_type": "village", "dweller_role": "inhabitant", "update_interval_base": 60, "update_interval_variant": 40, "can_find_poi": false, "can_migrate": true, "first_founding_reward": 5}}, "rza:tower_guard": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_ranged_gear.lt.json"}, "minecraft:behavior.ranged_attack": {"priority": 4, "attack_interval_min": 1, "attack_interval_max": 1, "attack_radius": 48, "speed_multiplier": 0.6, "attack_radius_min": 0}, "minecraft:shooter": {"def": "minecraft:arrow"}, "minecraft:behavior.charge_held_item": {"priority": 5, "items": ["minecraft:arrow"]}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 48, "entity_types": [{"priority": 0, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "operator": "!=", "value": "zombie_illager"}]}, "max_dist": 48}, {"priority": 2, "filters": {"any_of": [{"all_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "has_mob_effect", "subject": "other", "operator": "!=", "value": "weakness"}]}, {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie_illager"}, {"test": "has_mob_effect", "subject": "other", "operator": "!=", "value": "weakness"}]}]}, "max_dist": 48}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": "<=", "value": 3}, "event": "rza:melee_mode_tower_guard", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_tower_guard", "target": "self"}]}}, "transform_to_zombie": {"minecraft:transformation": {"into": "rza:zombie_pillager", "preserve_equipment": true, "delay": {"value": 0}}}, "rza:can_mingle": {"minecraft:behavior.mingle": {"priority": 2, "speed_multiplier": 0.5, "duration": 60, "cooldown_time": 10, "mingle_partner_type": ["minecraft:villager_v2", "minecraft:vindicator", "minecraft:pillager", "minecraft:evocation_illager"], "mingle_distance": 2}}, "minecraft:ranged_attack_iron": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_ranged_gear.lt.json"}, "minecraft:behavior.ranged_attack": {"priority": 4, "attack_interval_min": 1, "attack_interval_max": 1, "attack_radius": 16, "speed_multiplier": 0.6, "attack_radius_min": 0}, "minecraft:shooter": {"def": "minecraft:arrow"}, "minecraft:behavior.charge_held_item": {"priority": 5, "items": ["minecraft:arrow"]}, "minecraft:behavior.avoid_mob_type": {"priority": 4, "max_flee": 4, "sprint_distance": 1, "remove_target": true, "max_dist": 4, "ignore_visibility": true, "probability_per_strength": 0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "must_see": true, "must_see_forget_duration": 0, "max_dist": 4, "walk_speed_multiplier": 1, "sprint_speed_multiplier": 1}], "on_escape_event": {"event": "minecraft:ranged_mode_iron", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 16, "entity_types": [{"priority": 0, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 16}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": "<=", "value": 3}, "event": "minecraft:melee_mode_iron", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_iron", "target": "self"}]}}, "minecraft:ranged_attack_diamond": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_ranged_gear.lt.json"}, "minecraft:behavior.ranged_attack": {"priority": 4, "attack_interval_min": 1, "attack_interval_max": 1, "attack_radius": 16, "speed_multiplier": 0.6, "attack_radius_min": 0}, "minecraft:shooter": {"def": "minecraft:arrow"}, "minecraft:behavior.charge_held_item": {"priority": 5, "items": ["minecraft:arrow"]}, "minecraft:behavior.avoid_mob_type": {"priority": 4, "max_flee": 4, "sprint_distance": 1, "remove_target": true, "max_dist": 4, "ignore_visibility": true, "probability_per_strength": 0, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "must_see": true, "must_see_forget_duration": 0, "max_dist": 4, "walk_speed_multiplier": 1, "sprint_speed_multiplier": 1}], "on_escape_event": {"event": "minecraft:ranged_mode_diamond", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "within_radius": 16, "entity_types": [{"priority": 0, "filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 16}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": "<=", "value": 3}, "event": "minecraft:melee_mode_diamond", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_diamond", "target": "self"}]}}, "minecraft:melee_attack_iron": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_iron_melee_gear.lt.json"}, "minecraft:behavior.delayed_attack": {"priority": 3, "attack_duration": 0.5, "hit_delay_pct": 0, "cooldown_time": 0.7, "min_path_time": 0.01, "max_path_time": 0.02, "random_stop_interval": 999, "path_fail_time_increase": 0.01, "path_inner_boundary": 0.01, "path_outer_boundary": 0.01, "inner_boundary_time_increase": 0.01, "outer_boundary_time_increase": 0.01, "track_target": false}, "minecraft:attack": {"damage": 1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_reach": true, "must_see": true, "reselect_targets": true, "within_radius": 12, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 12}]}, "minecraft:behavior.drink_potion": {"priority": 15, "speed_modifier": -0.2, "potions": [{"id": 30, "chance": 0.5, "filters": {"all_of": [{"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}]}}, {"id": 19, "chance": 0.15, "filters": {"all_of": [{"test": "is_underwater", "subject": "self", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "water_breathing"}]}]}}, {"id": 12, "chance": 0.15, "filters": {"all_of": [{"any_of": [{"test": "on_fire", "subject": "self", "value": true}, {"test": "on_hot_block", "subject": "self", "value": true}, {"test": "taking_fire_damage", "subject": "self", "value": true}]}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "fire_resistance"}]}]}}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": ">=", "value": 3}, "event": "minecraft:ranged_mode_iron", "target": "self"}, {"filters": {"test": "has_target", "subject": "self", "value": false}, "event": "minecraft:ranged_mode_iron", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_iron", "target": "self"}]}}, "minecraft:melee_attack_diamond": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_diamond_melee_gear.lt.json"}, "minecraft:behavior.delayed_attack": {"priority": 3, "attack_duration": 0.5, "hit_delay_pct": 0, "cooldown_time": 0.7, "min_path_time": 0.01, "max_path_time": 0.02, "random_stop_interval": 999, "path_fail_time_increase": 0.01, "path_inner_boundary": 0.01, "path_outer_boundary": 0.01, "inner_boundary_time_increase": 0.01, "outer_boundary_time_increase": 0.01, "track_target": false}, "minecraft:attack": {"damage": 1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_reach": true, "must_see": true, "reselect_targets": true, "within_radius": 12, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 12}]}, "minecraft:behavior.drink_potion": {"priority": 15, "speed_modifier": -0.2, "potions": [{"id": 30, "chance": 0.5, "filters": {"all_of": [{"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}]}}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": ">=", "value": 3}, "event": "minecraft:ranged_mode_diamond", "target": "self"}, {"filters": {"test": "has_target", "subject": "self", "value": false}, "event": "minecraft:ranged_mode_diamond", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_diamond", "target": "self"}]}}, "minecraft:melee_attack_tower_guard": {"minecraft:equipment": {"table": "loot_tables/equipment/pillager_iron_melee_gear.lt.json"}, "minecraft:behavior.delayed_attack": {"priority": 3, "attack_duration": 0.5, "hit_delay_pct": 0, "cooldown_time": 0.7, "min_path_time": 0.01, "max_path_time": 0.02, "random_stop_interval": 999, "path_fail_time_increase": 0.01, "path_inner_boundary": 0.01, "path_outer_boundary": 0.01, "inner_boundary_time_increase": 0.01, "outer_boundary_time_increase": 0.01, "track_target": false}, "minecraft:attack": {"damage": 1}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_reach": true, "must_see": true, "reselect_targets": true, "within_radius": 12, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}]}, "max_dist": 12}]}, "minecraft:behavior.drink_potion": {"priority": 15, "speed_modifier": -0.2, "potions": [{"id": 30, "chance": 0.5, "filters": {"all_of": [{"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}]}}, {"id": 19, "chance": 0.15, "filters": {"all_of": [{"test": "is_underwater", "subject": "self", "value": true}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "water_breathing"}]}]}}, {"id": 12, "chance": 0.15, "filters": {"all_of": [{"any_of": [{"test": "on_fire", "subject": "self", "value": true}, {"test": "on_hot_block", "subject": "self", "value": true}, {"test": "taking_fire_damage", "subject": "self", "value": true}]}, {"none_of": [{"test": "has_mob_effect", "subject": "self", "value": "fire_resistance"}]}]}}]}, "minecraft:environment_sensor": {"triggers": [{"filters": {"test": "target_distance", "subject": "other", "operator": ">=", "value": 3}, "event": "rza:ranged_mode_tower_guard", "target": "self"}, {"filters": {"test": "has_target", "subject": "self", "value": false}, "event": "rza:ranged_mode_tower_guard", "target": "self"}, {"filters": {"test": "actor_health", "subject": "self", "operator": "<=", "value": 15}, "event": "rza:avoid_mobs_tower_guard", "target": "self"}]}}, "rza:avoid_mobs_iron": {"minecraft:is_hidden_when_invisible": {}, "minecraft:behavior.avoid_mob_type": {"priority": 0, "max_flee": 10, "sprint_distance": 1, "remove_target": true, "max_dist": 5, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "zombie"}, "max_dist": 4, "check_if_outnumbered": true, "must_see": true, "walk_speed_multiplier": 1, "sprint_speed_multiplier": 1}]}, "minecraft:behavior.drink_potion": {"priority": 1, "speed_modifier": -0.2, "potions": [{"id": 22, "chance": 1, "filters": {"all_of": [{"test": "is_missing_health", "subject": "self", "value": true}]}}]}, "minecraft:environment_sensor": {"triggers": {"filters": {"test": "actor_health", "subject": "self", "operator": ">=", "value": 20}, "event": "rza:on_avoid_iron", "target": "self"}}}, "rza:avoid_mobs_diamond": {"minecraft:is_hidden_when_invisible": {}, "minecraft:behavior.avoid_mob_type": {"priority": 0, "max_flee": 10, "sprint_distance": 1, "remove_target": true, "max_dist": 5, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "zombie"}, "max_dist": 4, "check_if_outnumbered": true, "must_see": true, "walk_speed_multiplier": 1, "sprint_speed_multiplier": 1}]}, "minecraft:behavior.drink_potion": {"priority": 1, "speed_modifier": -0.2, "potions": [{"id": 22, "chance": 1, "filters": {"all_of": [{"test": "is_missing_health", "subject": "self", "value": true}]}}]}, "minecraft:environment_sensor": {"triggers": {"filters": {"test": "actor_health", "subject": "self", "operator": ">=", "value": 20}, "event": "rza:on_avoid_diamond", "target": "self"}}}, "rza:avoid_mobs_tower_guard": {"minecraft:is_hidden_when_invisible": {}, "minecraft:behavior.avoid_mob_type": {"priority": 0, "max_flee": 10, "sprint_distance": 1, "remove_target": true, "max_dist": 5, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "zombie"}, "max_dist": 4, "check_if_outnumbered": true, "must_see": true, "walk_speed_multiplier": 1, "sprint_speed_multiplier": 1}]}, "minecraft:behavior.drink_potion": {"priority": 1, "speed_modifier": -0.2, "potions": [{"id": 22, "chance": 1, "filters": {"all_of": [{"test": "is_missing_health", "subject": "self", "value": true}]}}]}, "minecraft:environment_sensor": {"triggers": {"filters": {"test": "actor_health", "subject": "self", "operator": ">=", "value": 20}, "event": "rza:on_avoid_tower_guard", "target": "self"}}}, "minecraft:illager_squad_captain": {"minecraft:is_illager_captain": {}, "minecraft:variant": {"value": 1}, "minecraft:equipment": {"table": "loot_tables/entities/pillager_captain_equipment.json", "slot_drop_chance": [{"slot": "slot.armor.chest", "drop_chance": 1}]}}}, "events": {"minecraft:entity_spawned": {"sequence": [{"add": {"component_groups": ["rza:can_mingle", "rza:settler"]}}, {"randomize": [{"weight": 5, "add": {"component_groups": ["minecraft:ranged_attack_iron"]}}, {"weight": 3, "add": {"component_groups": ["minecraft:ranged_attack_diamond"]}}]}]}, "rza:as_tower_guard": {"remove": {"component_groups": ["rza:settler"]}, "add": {"component_groups": ["rza:tower_guard"]}}, "minecraft:entity_transformed": {"sequence": [{"add": {"component_groups": ["rza:can_mingle"]}}, {"filters": {"test": "has_equipment", "subject": "other", "value": "minecraft:iron_sword"}, "add": {"component_groups": ["minecraft:melee_attack_iron"]}}, {"filters": {"test": "has_equipment", "subject": "other", "value": "minecraft:diamond_sword"}, "add": {"component_groups": ["minecraft:melee_attack_diamond"]}}, {"filters": {"test": "has_equipment", "subject": "other", "value": "minecraft:crossbow"}, "randomize": [{"weight": 3, "add": {"component_groups": ["minecraft:ranged_attack_iron"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:ranged_attack_diamond"]}}]}]}, "minecraft:spawn_as_illager_captain": {"sequence": [{"add": {"component_groups": ["rza:can_mingle", "minecraft:illager_squad_captain"]}}, {"randomize": [{"weight": 3, "add": {"component_groups": ["minecraft:ranged_attack_iron"]}}, {"weight": 5, "add": {"component_groups": ["minecraft:ranged_attack_diamond"]}}]}]}, "minecraft:melee_mode_iron": {"remove": {"component_groups": ["minecraft:ranged_attack_iron"]}, "add": {"component_groups": ["minecraft:melee_attack_iron"]}}, "minecraft:melee_mode_diamond": {"remove": {"component_groups": ["minecraft:ranged_attack_diamond"]}, "add": {"component_groups": ["minecraft:melee_attack_diamond"]}}, "rza:melee_mode_tower_guard": {"remove": {"component_groups": ["rza:tower_guard"]}, "add": {"component_groups": ["minecraft:melee_attack_tower_guard"]}}, "minecraft:ranged_mode_iron": {"remove": {"component_groups": ["minecraft:melee_attack_iron"]}, "add": {"component_groups": ["minecraft:ranged_attack_iron"]}}, "minecraft:ranged_mode_diamond": {"remove": {"component_groups": ["minecraft:melee_attack_diamond"]}, "add": {"component_groups": ["minecraft:ranged_attack_diamond"]}}, "rza:ranged_mode_tower_guard": {"remove": {"component_groups": ["minecraft:melee_attack_tower_guard"]}, "add": {"component_groups": ["rza:tower_guard"]}}, "rza:avoid_mobs_iron": {"remove": {"component_groups": ["minecraft:melee_attack_iron", "minecraft:ranged_attack_iron"]}, "add": {"component_groups": ["rza:avoid_mobs_iron"]}}, "rza:avoid_mobs_diamond": {"remove": {"component_groups": ["minecraft:melee_attack_diamond", "minecraft:ranged_attack_diamond"]}, "add": {"component_groups": ["rza:avoid_mobs_diamond"]}}, "rza:avoid_mobs_tower_guard": {"remove": {"component_groups": ["minecraft:melee_attack_tower_guard", "rza:tower_guard"]}, "add": {"component_groups": ["rza:avoid_mobs_tower_guard"]}}, "rza:on_avoid_iron": {"remove": {"component_groups": ["rza:avoid_mobs_iron"]}, "add": {"component_groups": ["minecraft:ranged_attack_iron"]}}, "rza:on_avoid_diamond": {"remove": {"component_groups": ["rza:avoid_mobs_diamond"]}, "add": {"component_groups": ["minecraft:ranged_attack_diamond"]}}, "rza:on_avoid_tower_guard": {"remove": {"component_groups": ["rza:avoid_mobs_tower_guard"]}, "add": {"component_groups": ["rza:tower_guard"]}}, "rza:transform_to_zombie": {"add": {"component_groups": ["transform_to_zombie"]}}, "rza:can_mingle": {"add": {"component_groups": ["rza:can_mingle"]}}, "rza:cant_mingle": {"remove": {"component_groups": ["rza:can_mingle"]}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player?(query.is_baby?12:5)+(math.die_roll(query.equipment_count,1,3)):0"}, "minecraft:equip_item": {}, "minecraft:collision_box": {"width": 0.6, "height": 1.9}, "minecraft:type_family": {"family": ["pillager", "illager", "mob"]}, "minecraft:persistent": {}, "minecraft:variant": {"value": 0}, "minecraft:breathable": {"total_supply": 15, "suffocate_time": 0}, "minecraft:health": {"value": 24, "max": 24}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:loot": {"table": "loot_tables/entities/pillager.json"}, "minecraft:shareables": {"items": [{"item": "minecraft:banner:15", "want_amount": 1, "surplus_amount": 1, "priority": 0}]}, "minecraft:movement": {"value": 0.35}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true, "can_open_doors": true, "can_pass_doors": true}, "minecraft:behavior.open_door": {"priority": 2, "close_door_after": true}, "minecraft:annotation.open_door": {}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:nameable": {}, "minecraft:equipment": {"table": "loot_tables/entities/pillager_gear.json"}, "minecraft:despawn": {"despawn_from_distance": {}}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.move_to_land": {"priority": 2, "goal_radius": 0.5, "speed_multiplier": 1.1}, "minecraft:behavior.hurt_by_target": {"priority": 5, "entity_types": {"must_see": true, "must_see_forget_duration": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}}}, "minecraft:behavior.equip_item": {"priority": 3}, "minecraft:behavior.pickup_items": {"priority": 7, "max_dist": 3, "goal_radius": 2, "speed_multiplier": 1}, "minecraft:scheduler": {"min_delay_secs": 0, "max_delay_secs": 10, "scheduled_events": [{"filters": {"any_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "hourly_clock_time", "operator": "<=", "value": 8000}, {"test": "hourly_clock_time", "operator": ">", "value": 10000}]}, "event": "rza:cant_mingle"}, {"filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "hourly_clock_time", "operator": ">=", "value": 8000}, {"test": "hourly_clock_time", "operator": "<", "value": 10000}]}, "event": "rza:can_mingle"}]}, "minecraft:behavior.random_stroll": {"priority": 8, "speed_multiplier": 1}, "minecraft:behavior.look_at_player": {"priority": 9, "look_distance": 1}, "minecraft:behavior.random_look_around": {"priority": 10}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": true, "is_pushable_by_piston": true}, "minecraft:follow_range": {"value": 64}, "minecraft:damage_sensor": {"triggers": [{"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "zombie"}], "all_of": [{"test": "has_damage", "value": "fatal"}]}, "event": "rza:transform_to_zombie"}}, {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}]}, "minecraft:conditional_bandwidth_optimization": {}}}}