{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:witherator_skull_dangerous", "is_spawnable": false, "is_summonable": true, "is_experimental": false, "runtime_identifier": "minecraft:snowball", "properties": {"rza:trackable": {"client_sync": false, "type": "int", "default": 10, "range": [0, 10]}, "rza:rotation_x": {"type": "float", "default": 0.0, "range": [-360.0, 360.0], "client_sync": true}, "rza:rotation_y": {"type": "float", "default": 0.0, "range": [-360.0, 360.0], "client_sync": true}}}, "component_groups": {"rza:despawn": {"minecraft:instant_despawn": {"remove_child_entities": true}}}, "events": {"rza:explode": {}, "rza:despawn": {"add": {"component_groups": ["rza:despawn"]}}}, "components": {"minecraft:collision_box": {"width": 0.001, "height": 0.001}, "minecraft:timer": {"time": 10, "looping": false, "time_down_event": {"event": "rza:despawn", "target": "self"}}, "minecraft:projectile": {"on_hit": {"definition_event": {"affect_projectile": false, "event_trigger": {"event": "rza:explode", "target": "self"}}}, "power": 0.6, "gravity": 0.0, "uncertainty_base": 0, "uncertainty_multiplier": 1, "anchor": 1, "offset": [0, -0.1, 0], "is_dangerous": true, "inertia": 0.98, "liquid_inertia": 1.0, "shoot_target": false, "reflect_on_hurt": true}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:conditional_bandwidth_optimization": {"default_values": {"max_optimized_distance": 128.0, "max_dropped_ticks": 1, "use_motion_prediction_hints": true}}}}}