{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "rza:charged_iron_golem", "is_spawnable": true, "is_summonable": true, "is_experimental": false, "runtime_identifier": "iron_golem", "properties": {"rza:upgrade_phases": {"client_sync": true, "type": "int", "range": [0, 2], "default": 0}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:type_family": {"family": ["charged_irongolem", "irongolem", "mob"]}, "minecraft:nameable": {}, "minecraft:collision_box": {"width": 1.4, "height": 2.9}, "minecraft:loot": {"table": "loot_tables/entities/iron_golem.json"}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.25}, "minecraft:navigation.walk": {"can_path_over_water": false, "avoid_water": true, "is_amphibious": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:interact": {"interactions": [{"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "iron_ingot"}, {"test": "is_missing_health", "value": true}]}}, "use_item": true, "swing": true, "health_amount": 25, "play_sounds": "irongolem.repair", "interact_text": "action.interact.repair"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "tnt"}, {"test": "int_property", "domain": "rza:upgrade_phases", "subject": "self", "operator": "<", "value": 2}]}, "event": "rza:upgrade", "target": "self"}, "use_item": true, "swing": true, "play_sounds": "irongolem.repair", "interact_text": "action.interact.upgrade"}, {"on_interact": {"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "player"}, {"test": "has_equipment", "domain": "hand", "subject": "other", "value": "nether_star"}, {"test": "int_property", "domain": "rza:upgrade_phases", "subject": "self", "operator": "==", "value": 2}]}, "event": "rza:to_super_iron_golem", "target": "self"}, "use_item": true, "swing": true, "play_sounds": "irongolem.max_upgrade", "interact_text": "action.interact.upgrade"}]}, "minecraft:attack": {"damage": {"range_min": 7, "range_max": 21}}, "minecraft:damage_sensor": {"triggers": [{"cause": "fall", "deals_damage": "no"}, {"cause": "block_explosion", "deals_damage": "no"}, {"cause": "entity_explosion", "deals_damage": "no"}, {"on_damage": {"filters": {"any_of": [{"test": "is_family", "subject": "other", "value": "turret"}, {"test": "is_family", "subject": "other", "value": "illager"}, {"test": "is_family", "subject": "other", "value": "villager"}, {"test": "is_family", "subject": "other", "value": "wandering_trader"}]}}, "deals_damage": "no"}]}, "minecraft:knockback_resistance": {"value": 1}, "minecraft:leashable": {}, "minecraft:leashable_to": {}, "minecraft:balloonable": {"mass": 2}, "minecraft:behavior.melee_attack": {"priority": 1, "track_target": false, "reach_multiplier": 2}, "minecraft:behavior.move_towards_target": {"priority": 2, "speed_multiplier": 0.9, "within_radius": 32}, "minecraft:behavior.move_through_village": {"priority": 3, "speed_multiplier": 0.6, "only_at_night": true}, "minecraft:behavior.random_stroll": {"priority": 6, "speed_multiplier": 0.6, "xz_dist": 16}, "minecraft:behavior.look_at_player": {"priority": 7, "look_distance": 6, "probability": 0.02}, "minecraft:behavior.random_look_around": {"priority": 8}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 2, "must_see": true, "must_reach": true, "persist_time": 0, "reselect_targets": true, "within_radius": 12, "reevaluate_description": true, "entity_types": [{"filters": {"all_of": [{"test": "is_family", "subject": "other", "value": "zombie"}, {"none_of": [{"all_of": [{"any_of": [{"test": "is_family", "subject": "other", "value": "zombie_villager"}, {"test": "is_family", "subject": "other", "value": "zombie_illager"}]}, {"test": "has_mob_effect", "subject": "other", "value": "weakness"}, {"test": "has_component", "subject": "other", "value": "minecraft:transformation"}]}]}]}, "max_dist": 12}]}, "minecraft:persistent": {}, "minecraft:physics": {}, "minecraft:pushable": {"is_pushable": false, "is_pushable_by_piston": true}, "minecraft:follow_range": {"value": 64}, "minecraft:on_death": {"event": "rza:explode", "target": "self"}, "minecraft:conditional_bandwidth_optimization": {}}, "component_groups": {"rza:level1": {"minecraft:health": {"value": 200}}, "rza:level2": {"minecraft:health": {"value": 400}}, "rza:level3": {"minecraft:health": {"value": 800}}, "rza:to_supercharged_iron_golem": {"minecraft:transformation": {"into": "rza:supercharged_iron_golem", "delay": 0}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["rza:level1"]}}, "minecraft:entity_transformed": {"add": {"component_groups": ["rza:level1"]}}, "rza:upgrade": {"sequence": [{"filters": {"test": "int_property", "subject": "self", "domain": "rza:upgrade_phases", "operator": "==", "value": 0}, "sequence": [{"remove": {"component_groups": ["rza:level1"]}}, {"add": {"component_groups": ["rza:level2"]}}, {"set_property": {"rza:upgrade_phases": "query.property('rza:upgrade_phases') + 1"}}]}, {"filters": {"test": "int_property", "subject": "self", "domain": "rza:upgrade_phases", "operator": "==", "value": 1}, "sequence": [{"remove": {"component_groups": ["rza:level2"]}}, {"add": {"component_groups": ["rza:level3"]}}, {"set_property": {"rza:upgrade_phases": "query.property('rza:upgrade_phases') + 1"}}]}]}, "rza:to_super_iron_golem": {"add": {"component_groups": ["rza:to_supercharged_iron_golem"]}}, "rza:heal": {"queue_command": {"command": "effect @s regeneration 2 5"}}, "rza:explode": {"queue_command": {"command": ["summon rza:iron_golem_explosions ~ ~ ~"]}}}}}