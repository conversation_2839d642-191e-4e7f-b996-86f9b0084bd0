{"format_version": "1.10.0", "animations": {"animation.alpha.normal.particles_regular": {"animation_length": 0, "loop": true, "particle_effects": {"0.0": {"effect": "particles_regular"}}}, "animation.alpha.normal.particles_mutated": {"animation_length": 0, "loop": true, "particle_effects": {"0.0": {"effect": "particles_mutated"}}}, "animation.alpha.normal.damage": {"animation_length": 0.5, "bones": {"spine": {"rotation": {"0.0": [0, 0, 0], "0.125": [-50, 0, 0], "0.375": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-82.5, 0, 0], "0.5": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-82.5, 0, 0], "0.5": [0, 0, 0]}}}}, "animation.alpha.normal.idle1": {"loop": true, "bones": {"root": {"rotation": ["math.sin(q.anim_time * 64) * 2", 0, 0], "position": [0, -0.5, 0]}, "spine": {"rotation": ["25+math.sin((q.anim_time + 3.5) * 64) * 6", "math.sin((q.anim_time + 3.5) * 32) * 6", "math.sin((q.anim_time + 3.5) * 32) * 6"]}, "rightArm": {"rotation": ["-49.4966+math.sin((q.anim_time + 7) * 64) * 7", "17.8919+math.sin((q.anim_time + 4) * 64) * 7", "-13.7399+math.sin((q.anim_time + 5) * 64) * 7"]}, "leftArm": {"rotation": ["-49.4966+math.sin((q.anim_time + 4) * 64) * 7", "17.8919+math.sin((q.anim_time + 4) * 64) * 7", "-13.7399+math.sin((q.anim_time + 6) * 64) * 7"]}, "right_leg": {"rotation": [-8.6422, 27.98203, 10.74647]}, "left_leg": {"rotation": [7.5, 0, -10]}}}, "animation.alpha.normal.idle2": {"loop": true, "bones": {"root": {"rotation": ["-2.5+math.sin((q.anim_time + 2) * 64) * 2", 0, "math.sin((q.anim_time + 2) * 32) * 2"], "position": [0, -0.5, 0]}, "spine": {"rotation": ["-12.5+math.sin(q.anim_time * 64) * 6", 0, 0]}, "head": {"rotation": ["22.5+math.clamp(math.sin((q.anim_time + 4) * 64) * 24, -24, 12)", 0, 0]}, "rightArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 64) * 12", 0, 0]}, "leftArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 64) * 6", 0, 0]}, "right_leg": {"rotation": [17.5, 0, 10], "position": [0, 0, 0]}, "left_leg": {"rotation": [-17.64892, 1.72794, -9.85108]}}}, "animation.alpha.normal.idle3": {"loop": true, "bones": {"root": {"rotation": ["-2.5+math.sin((q.anim_time + 2) * 64) * 2", 0, 0], "position": [0, -0.5, 0]}, "spine": {"rotation": ["-12.5+math.sin(q.anim_time * 64) * 6", 0, 0]}, "head": {"rotation": ["22.5+math.clamp(math.sin((q.anim_time + 4) * 32) * 24, -24, 24)", "math.clamp(math.sin((q.anim_time + 4) * 32) * 48, -12, 0)", "math.sin((q.anim_time + 3) * 32) * 8"]}, "rightArm": {"rotation": ["-17.5+math.sin((q.anim_time + 4) * 64) * 12", 0, 0]}, "leftArm": {"rotation": ["-12.7928+math.sin((q.anim_time + 4) * 64) * 24", "-2.9357+math.sin((q.anim_time + 2) * 64) * 7", "-4.0574+math.sin((q.anim_time + 2) * 64) * 3"]}, "right_leg": {"rotation": [17.5, 0, 10], "position": [0, 0, 0]}, "left_leg": {"rotation": [-17.64892, 1.72794, -9.85108]}}}, "animation.alpha.normal.idle4": {"loop": true, "bones": {"root": {"rotation": ["math.sin(q.anim_time * 64) * 2", 0, "math.sin(q.anim_time * 32) * 2"], "position": [0, -0.5, 0]}, "spine": {"rotation": ["25+math.sin((q.anim_time + 3.5) * 64) * 6", "math.sin((q.anim_time + 3.5) * 32) * 6", "math.sin((q.anim_time + 3.5) * 32) * 6"]}, "rightArm": {"rotation": ["-49.4966+math.sin((q.anim_time + 7) * 64) * 7", "17.8919+math.sin((q.anim_time + 4) * 64) * 7", "-13.7399-math.sin((q.anim_time + 5) * 64) * 7"]}, "leftArm": {"rotation": ["-49.4966-math.sin((q.anim_time + 4) * 64) * 7", "17.8919-math.sin((q.anim_time + 4) * 64) * 7", "-13.7399+math.sin((q.anim_time + 6) * 64) * 7"]}, "right_leg": {"rotation": [-8.6422, 27.98203, 10.74647]}, "left_leg": {"rotation": [7.5, 0, -10]}, "head": {"rotation": ["-17.5+math.sin((q.anim_time + 3) * 64) * 8", 0, "math.sin((q.anim_time + 3) * 32) * 8"]}}}, "animation.alpha.normal.idle5": {"loop": true, "bones": {"root": {"rotation": [0, 0, "math.sin(q.anim_time * 32) * 2"], "position": [0, -0.5, 0]}, "spine": {"rotation": [0, "math.sin((q.anim_time + 3.5) * 32) * 6", "math.sin((q.anim_time + 3.5) * 32) * 6"]}, "rightArm": {"rotation": ["-49.4966-math.sin((q.anim_time + 4) * 64) * 7", 0, "13.7399+math.sin((q.anim_time + 6) * 64) * 7"]}, "leftArm": {"rotation": ["-49.4966-math.sin((q.anim_time + 4) * 64) * 7", 0, "-13.7399+math.sin((q.anim_time + 6) * 64) * 7"]}, "right_leg": {"rotation": [-8.6422, 27.98203, 10.74647]}, "left_leg": {"rotation": [7.5, 0, -10]}, "head": {"rotation": ["math.sin((q.anim_time + 3) * 64) * 8", 0, "math.cos((q.anim_time + 3) * 32) * 8"]}}}, "animation.alpha.normal.walk1": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"spine": {"rotation": ["7.5+math.cos((q.anim_time + 7) * 600) * 4", "math.sin((q.anim_time + 1.5) * 200) * 8", "math.sin((q.anim_time + 1.5) * 100) * 4"], "position": [0, "-math.sin(q.anim_time * 600) * 0.5", 0]}, "head": {"rotation": ["7.5+math.sin((q.anim_time + 4) * 600) * 8", 0, 0]}, "rightArm": {"rotation": ["-75+math.sin((q.anim_time + 8) * 300) * 12", 0, 0]}, "leftArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 300) * 24", 0, 0]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [0, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [0, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.alpha.normal.walk2": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"spine": {"rotation": ["7.5+math.cos(q.anim_time * 600)", "math.sin((q.anim_time + 1.5) * 100) * 12", "math.sin((q.anim_time + 1.5) * 300) * 4"], "position": [0, "-math.sin(q.anim_time * 600) * 0.5", 0]}, "head": {"rotation": ["7.5+math.sin((q.anim_time + 4) * 600) * 4", 0, 0]}, "rightArm": {"rotation": ["-75+math.sin((q.anim_time + 6) * 300) * 7", 0, 0]}, "leftArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 300) * 7", 0, 0]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [0, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [0, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.alpha.normal.walk3": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"spine": {"rotation": ["-10+math.cos(q.anim_time * 30) * 24", "math.sin((q.anim_time + 1.5) * 200) * 4", "math.sin((q.anim_time + 1.5) * 100) * 4"], "position": [0, "-math.sin(q.anim_time * 600) * 0.5", 0]}, "head": {"rotation": ["7.5+math.sin((q.anim_time + 4) * 600) * 4", 0, 0]}, "rightArm": {"rotation": ["-60-math.sin((q.anim_time + 5) * 20) * 48", 0, "math.sin((q.anim_time + 2) * 300) * 8"]}, "leftArm": {"rotation": ["-12.8895+math.sin((q.anim_time + 1) * 300) * 28", -3.98105, -16.89721]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [0, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [0, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.alpha.normal.walk4": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"spine": {"rotation": ["7.5+math.cos(q.anim_time * 50) * 32", "math.sin((q.anim_time + 1.5) * 100) * 12", "math.sin((q.anim_time + 1.5) * 300) * 8"], "position": [0, "-math.sin(q.anim_time * 600) * 0.5", 0]}, "head": {"rotation": ["7.5+math.sin((q.anim_time + 4) * 600) * 4", 0, 0]}, "rightArm": {"rotation": ["-57.5+math.sin((q.anim_time + 1) * 40) * 48", 0, 0]}, "leftArm": {"rotation": ["-27.5+math.sin((q.anim_time + 6) * 60) * 48", 0, 0]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [0, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [0, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.alpha.normal.walk5": {"loop": true, "anim_time_update": "q.anim_time+(q.delta_time*(q.modified_move_speed*2))", "bones": {"spine": {"rotation": ["7.5+math.cos(q.anim_time * 600)", 0, "math.sin((q.anim_time + 1.5) * 300) * 3"], "position": [0, "-math.sin(q.anim_time * 600) * 0.5", 0]}, "head": {"rotation": ["7.5+math.sin((q.anim_time + 4) * 600) * 4", 0, 0]}, "rightArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 300) * 8", 0, 0]}, "leftArm": {"rotation": ["-75+math.sin((q.anim_time + 4) * 300) * 8", 0, 0]}, "right_leg": {"rotation": ["0.0226-math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, 2.20768], "position": [0, "math.clamp((-math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((-math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}, "left_leg": {"rotation": ["0.0226+math.sin((q.anim_time + 11.1) * 300) * 28", 1.17339, -2.2077], "position": [0, "math.clamp((math.sin(q.anim_time * 300) * 2), 0, 4)", "-0.25+math.clamp((math.sin(q.anim_time * 300) * 0.5), -0.3, 0.5)"]}}}, "animation.alpha.normal.attack1": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"spine": {"rotation": {"0.0": [40, 0, 0], "0.1667": [-17.5, 0, 0], "0.5": [40, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.1667": [30, 0, 0], "0.3333": [-30, 0, 0], "0.5": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-45, 0, 0], "0.25": [-175, 0, 0], "0.375": [-149.51568, -10.90183, -38.52907], "0.5": [0, 0, 0]}}, "leftArm": {"rotation": {"0.0": [-45, 0, 0], "0.25": [-175, 0, 0], "0.375": [-149.51568, 10.90183, 38.52907], "0.5": [0, 0, 0]}}}}, "animation.alpha.normal.attack2": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"spine": {"rotation": {"0.0": [40, 0, 0], "0.1667": [-22.61557, 0.75155, -17.48436], "0.5": [40, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.125": [27.5, 0, 0], "0.2083": [27.5, 0, 0], "0.3333": [-22.5, 0, 0], "0.5": [0, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-52.5, 0, 0], "0.25": [-165, 0, 0], "0.4167": [-193.40052, 9.57658, -39.02504], "0.5": [-52.5, 0, 0]}}, "leftArm": {"rotation": {"0.0": [-62.5, 0, 0], "0.3333": [-50, 0, 22.5], "0.5": [-62.5, 0, 0]}}}}, "animation.alpha.normal.attack3": {"loop": "hold_on_last_frame", "animation_length": 0.5, "bones": {"spine": {"rotation": {"0.0": [0, 0, 0], "0.3333": [-19.17, 0, 0], "0.5": [52.5, 0, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.125": [15, 0, 0], "0.2083": [15, 0, 0], "0.4167": [-25, 0, 0], "0.5": [10, 0, 0]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-56.50703, 10.54529, 10.72858], "0.25": [-5.7128, 3.92941, -9.62437], "0.3333": [35, 0, 0], "0.4167": [50, 0, 0], "0.5": [17.5, 0, 0]}}, "rightArm": {"rotation": {"0.0": [-30, 0, 0], "0.1667": [-44.00703, -10.54529, -10.72858], "0.25": [-5.7128, -3.92941, 9.62437], "0.3333": [35, 0, 0], "0.4167": [50, 0, 0], "0.5": [17.5, 0, 0]}}}}, "animation.alpha.normal.death1": {"loop": "hold_on_last_frame", "animation_length": 0.7917, "override_previous_animation": true, "bones": {"head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [25, 0, 0], "0.5": [25, 0, 0], "0.5833": [0, -22.5, 0], "0.6667": [18.29388, -16.66577, -5.41615], "0.75": [9.36302, -31.26463, -17.62528]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, 54.35477, 11.00468], "0.4583": [-45, 0, 67.5], "0.5": [-45.50968, 1.98351, 90.46651], "0.5833": [-45.27854, 5.5444, 93.98467], "0.6667": [-44.82485, 9.08427, 97.54544], "0.75": [-45.54305, 0.19944, 88.71481]}, "position": {"0.0": [0, 0, 0], "0.4167": [-6, -18, 4], "0.5": [-6, -21, 4], "0.5833": [-6.5, -19.5, 4], "0.6667": [-7, -20.75, 4]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, -54.35477, -11.00468], "0.5": [-69.05898, -40.78947, -7.86119], "0.5833": [-86.55898, -40.78947, -7.86119]}, "position": {"0.0": [0, 0, 0], "0.4167": [0, -19, 14], "0.5": [0, -20.5, 14], "0.5833": [0, -20, 14.5], "0.6667": [0, -20.5, 15.25]}}, "left_leg": {"rotation": {"0.0": [0, 0, -17.5], "0.2917": [0, 0, 2.5]}, "position": {"0.5": [4, 0, 0], "0.625": [4, 0.75, 0.5], "0.75": [4, 0.75, 0]}}, "right_leg": {"rotation": [0, 0, 12.5], "position": {"0.0": [-3, 0, 0], "0.5": [-4, 0, 0], "0.5833": [-4, 0.5, 0.5], "0.75": [-4, 0.5, 0]}}, "spine": {"rotation": {"0.0": [0, 0, 0], "0.5": [-90, 0, 0]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, -0.23582, 0.16606], "0.0833": [0, -0.5605, 0.52408], "0.125": [0, -0.99572, 0.97599], "0.1667": [0, -1.57357, 1.47714], "0.2083": [0, -2.34399, 2], "0.25": [0, -3.39135, 2.52286], "0.2917": [0, -4.86985, 3.02401], "0.3333": [0, -7.02408, 3.47592], "0.375": [0, -9.32135, 3.83394], "0.4167": [0, -10, 4], "0.5": [0, -9, 5], "0.5833": [0, -10, 6]}}, "waist": {"rotation": {"0.0": [0, 0, 0.01], "0.1667": [47.5, 0.00737, 0.00676], "0.5": [90, 0.01, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0]}}}}, "animation.alpha.normal.death2": {"loop": "hold_on_last_frame", "animation_length": 0.9167, "override_previous_animation": true, "anim_time_update": "q.anim_time+q.delta_time*1.1", "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.125": [-17.5, 0, 0], "0.75": [90, 0, 0], "0.8333": [87.5, 0, 0], "0.9167": [90, 0, 0]}, "position": {"0.5417": [0, 0, 0], "0.75": [0, 2, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [25, 0, 0], "0.4167": [25, 0, 0], "0.5": [0, -52.5, 0], "0.5833": [90.65973, -67.47741, -101.86262], "0.75": [0, -90, 0], "0.8333": [90, -85.5, -90], "0.9167": [0, -90, 0]}, "position": {"0.0": [0, 0, 0], "0.125": [0, 1, 0], "0.1667": [0, -0.94, 5.28], "0.2083": [0, -4.14, 10.18], "0.25": [0, -6.83, 13.9], "0.2917": [0, -10.53, 16.79], "0.3333": [0, -14.72, 18.53], "0.375": [0, -20.65, 18.48], "0.4167": [0, -27.99, 16.09], "0.4583": [0, -33.96, 12.95], "0.5": [0, -35.25, 11.67], "0.5417": [0, -36.79, 10.39], "0.5833": [0, -38.03, 8.51], "0.75": [0, -40.25, 0.75], "0.8333": [0, -40.25, 1.25], "0.9167": [0, -40.25, 0.75]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-80, 0, 1.75926], "0.25": [0, 0, 0], "0.625": [45.31509, -18.15489, -64.98614], "0.75": [45.31509, -18.15489, -64.98614], "0.8333": [-2.18491, -18.15489, -64.98614]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, 54.35477, 11.00468], "0.3333": [0, 0, 67.5], "0.625": [27.5, 0, 67.5], "0.75": [27.5, 0, 67.5], "0.8333": [2.5, 0, 67.5]}}, "right_leg": {"rotation": {"0.0": [0, 0, 0], "0.1667": [18.78567, -14.26008, -10.25103], "0.375": [-4.84831, -38.90277, -24.0966], "0.5833": [12.0155, -5.8612, -57.57165], "0.8333": [1.91389, 1.21128, -55.07198]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 1, 0], "0.5": [0, 2, 1], "0.7083": [0, 2, 1], "0.8333": [0, 2, -0.75]}}, "left_leg": {"rotation": {"0.25": [0, 0, 0], "0.4583": [15, 0, 0], "0.5833": [0, 0, 0]}}}}, "animation.alpha.normal.death3": {"loop": "hold_on_last_frame", "animation_length": 0.7917, "override_previous_animation": true, "bones": {"root": {"rotation": {"0.0": [0, 0, 0.01], "0.1667": [-47.5, 0.00737, 0.00676], "0.5": [-90, 0.01, 0]}, "position": {"0.0": [0, 0, 0], "0.5": [0, 2, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [25, 0, 0], "0.5": [25, 0, 0], "0.5833": [0, -22.5, 0], "0.6667": [18.29388, -16.66577, -5.41615], "0.75": [9.36302, -31.26463, -17.62528]}, "position": {"0.0": [0, 0, 0], "0.0417": [0, -4.42, -9.75], "0.0833": [0, -8.84, -14.33], "0.125": [0, -14.86, -16.94], "0.1667": [0, -23.59, -15.84], "0.2083": [0, -27.78, -13.74], "0.25": [0, -30.72, -12.64], "0.2917": [0, -33.48, -10.54], "0.3333": [0, -35.04, -8.54], "0.375": [0, -36.49, -6.65], "0.4167": [0, -37.82, -3.88], "0.4583": [0, -39.01, -2.4], "0.5": [0, -39.03, -1.08], "0.7083": [0, -39.12, -0.5]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, 54.35477, 11.00468], "0.5": [-45, 0, 67.5], "0.625": [0, 22.5, 45], "0.7083": [-7.5, 22.5, 45], "0.7917": [0, 22.5, 45]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, -54.35477, -11.00468], "0.5": [-45, 0, -67.5], "0.625": [0, -22.5, -45], "0.7083": [-7.5, -22.5, -45], "0.7917": [0, -22.5, -45]}}, "left_leg": {"rotation": {"0.0": [0, 0, -2.5], "0.25": [-27.5, 0, 17.5], "0.4167": [-6.66667, 0, 17.5], "0.5": [0, 0, 17.5], "0.5833": [-2.5, 0, 17.5], "0.6667": [0, 0, 17.5]}, "position": {"0.0": [2, 0, 0], "0.25": [0, 0, -1], "0.4167": [0, 0, 0]}}, "right_leg": {"rotation": [0, 0, 12.5], "position": [-3, 0, 0]}}}, "animation.alpha.normal.death4": {"loop": "hold_on_last_frame", "animation_length": 1.125, "override_previous_animation": true, "anim_time_update": "q.anim_time+q.delta_time*1.1", "bones": {"root": {"rotation": {"0.0": [0, 0, 0], "0.125": [-17.5, 0, 0], "0.75": [90, 0, 0], "0.8333": [87.5, 0, 0], "0.9167": [90, 0, 0]}, "position": {"0.5417": [0, 0, 0], "0.75": [0, 2, 0]}}, "head": {"rotation": {"0.0": [0, 0, 0], "0.0833": [25, 0, 0], "0.4583": [0, -90, 0], "0.75": [0, -90, 0], "0.875": [89.99999, -82.5, -89.99999], "1.0": [-89.99999, -85, 89.99999], "1.125": [0, -90, 0]}}, "leftArm": {"rotation": {"0.0": [0, 0, 0], "0.0833": [-80, 0, 1.75926], "0.25": [0, 0, 0], "0.625": [45.31509, -18.15489, -64.98614], "0.75": [45.31509, -18.15489, -64.98614], "0.8333": [-2.18491, -18.15489, -64.98614]}}, "rightArm": {"rotation": {"0.0": [0, 0, 0], "0.125": [-51.89774, 54.35477, 11.00468], "0.3333": [0, 0, 67.5], "0.625": [27.5, 0, 67.5], "0.75": [27.5, 0, 67.5], "0.8333": [2.5, 0, 67.5]}}, "right_leg": {"rotation": {"0.0": [0, 0, 0], "0.1667": [-7.17277, 5.03142, -0.99399], "0.4167": [-99.18193, 12.57854, -2.48497], "0.5417": [-144.1914, 5.07928, -2.59093], "0.75": [-180.86595, -1.17011, -2.67923], "0.7917": [-179.20086, -2.41998, -2.69689]}, "position": {"0.0": [0, 0, 0], "0.1667": [0, 1.25, 0], "0.5417": [0, 1.25, 1.25], "0.7917": [0, -0.75, 0]}}, "left_leg": {"rotation": {"0.25": [0, 0, 0], "0.4583": [15, 0, 0], "0.5833": [0, 0, 0]}}}}}}