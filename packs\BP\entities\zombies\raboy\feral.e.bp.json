{
  "format_version": "1.21.90",
  "minecraft:entity": {
    "description": {
      "identifier": "rza:feral",
      "is_spawnable": true,
      "is_summonable": true,
      "is_experimental": false,
      "properties": { "rza:mutated": { "client_sync": true, "type": "bool", "default": false } },
      "animations": { "leap": "controller.animation.feral.leap" },
      "scripts": { "animate": [{ "leap": "q.property('rza:mutated') == true" }] }
    },
    "component_groups": {
      "rza:alert_others": {
        "minecraft:angry": {
          "broadcast_anger": true,
          "broadcast_range": 48,
          "broadcast_targets": ["zombie"],
          "duration": 8,
          "broadcast_filters": {
            "all_of": [
              { "test": "has_target", "subject": "other", "operator": "!=", "value": false },
              { "none_of": [{ "test": "has_mob_effect", "subject": "other", "value": "weakness" }] }
            ]
          },
          "filters": {
            "all_of": [
              { "test": "is_visible", "subject": "other", "value": true },
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "zombie" },
              { "test": "is_family", "subject": "other", "operator": "!=", "value": "turret" }
            ]
          },
          "calm_event": { "event": "rza:idle", "target": "self" }
        }
      },
      "rza:feral_regular": {
        "minecraft:health": { "value": 25, "max": 25 },
        "minecraft:attack": { "damage": 3 },
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(1,12)):0" }
      },
      "rza:feral_mutated": {
        "minecraft:health": { "value": 50, "max": 50 },
        "minecraft:attack": { "damage": 4 },
        "minecraft:experience_reward": { "on_death": "query.last_hit_by_player?8+(query.equipment_count*math.random(8,24)):0" }
      }
    },
    "events": {
      "minecraft:entity_spawned": {
        "sequence": [
          { "add": { "component_groups": ["rza:feral_regular"] } },
          { "queue_command": { "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate" } }
        ]
      },
      "minecraft:entity_transformed": {
        "sequence": [
          { "add": { "component_groups": ["rza:feral_regular"] } },
          { "queue_command": { "command": "execute if score main mutated_zombies matches 1 run event entity @s rza:mutate" } },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~-1"] } }], "weight": 3 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "easy" },
                { "test": "moon_phase", "operator": "!=", "value": 0 }
              ]
            }
          },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~-1"] } }], "weight": 5 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "normal" },
                { "test": "moon_phase", "operator": "!=", "value": 0 }
              ]
            }
          },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~-1"] } }], "weight": 10 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "hard" },
                { "test": "moon_phase", "operator": "!=", "value": 0 }
              ]
            }
          },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~1"] } }], "weight": 20 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "easy" },
                { "test": "moon_phase", "operator": "==", "value": 0 }
              ]
            }
          },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~1"] } }], "weight": 30 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "normal" },
                { "test": "moon_phase", "operator": "==", "value": 0 }
              ]
            }
          },
          {
            "randomize": [{ "weight": 100 }, { "sequence": [{ "queue_command": { "command": ["summon rza:feral ~1 ~ ~1"] } }], "weight": 40 }],
            "filters": {
              "all_of": [
                { "test": "is_difficulty", "operator": "==", "value": "hard" },
                { "test": "moon_phase", "operator": "==", "value": 0 }
              ]
            }
          }
        ]
      },
      "rza:mutate": {
        "sequence": [
          //When not on full moon
          {
            "filters": { "test": "moon_phase", "operator": "!=", "value": 0 },
            "randomize": [
              { "weight": 100 },
              {
                "weight": 10,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "easy" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              },
              {
                "weight": 20,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "normal" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              },
              {
                "weight": 40,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "hard" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              }
            ]
          },
          //During full moon
          {
            "filters": { "test": "moon_phase", "operator": "==", "value": 0 },
            "randomize": [
              { "weight": 100 },
              {
                "weight": 25,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "easy" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              },
              {
                "weight": 40,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "normal" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              },
              {
                "weight": 60,
                "filters": { "test": "is_difficulty", "operator": "==", "value": "hard" },
                "sequence": [
                  { "set_property": { "rza:mutated": true } },
                  { "remove": { "component_groups": ["rza:feral_regular"] } },
                  { "add": { "component_groups": ["rza:feral_mutated"] } }
                ]
              }
            ]
          }
        ]
      },
      "rza:leap": {},
      "rza:alert_others": { "add": { "component_groups": ["rza:alert_others"] } },
      "rza:idle": { "remove": { "component_groups": ["rza:alert_others"] } }
    },
    "components": {
      "minecraft:is_hidden_when_invisible": {},
      "minecraft:nameable": {},
      "minecraft:type_family": { "family": ["zombie", "feral", "undead", "monster", "mob"] },
      "minecraft:collision_box": { "width": 0.8, "height": 0.8 },
      "minecraft:movement": { "value": 0.25 },
      "minecraft:movement.basic": {},
      "minecraft:navigation.climb": {
        "is_amphibious": true,
        "can_pass_doors": true,
        "can_walk": true,
        "can_path_over_water": true,
        "avoid_damage_blocks": true,
        "can_jump": false
      },
      "minecraft:jump.static": {},
      "minecraft:can_climb": {},
      "minecraft:behavior.float": { "priority": 5 },
      "minecraft:hurt_on_condition": {
        "damage_conditions": [
          { "filters": { "test": "in_lava", "subject": "self", "operator": "==", "value": true }, "cause": "lava", "damage_per_tick": 4 }
        ]
      },
      "minecraft:damage_sensor": {
        "triggers": [
          { "cause": "fall", "deals_damage": "no" },
          { "on_damage": { "filters": { "any_of": [{ "test": "is_family", "subject": "other", "value": "zombie" }] } }, "deals_damage": "no" }
        ]
      },
      "minecraft:breathable": { "total_supply": 15, "suffocate_time": 0, "breathes_air": true, "breathes_water": true },
      "minecraft:loot": { "table": "loot_tables/entities/zombies/feral.lt.json" },
      "minecraft:despawn": { "despawn_from_distance": { "max_distance": 64, "min_distance": 32 } },
      "minecraft:behavior.delayed_attack": {
        "priority": 3,
        "speed_multiplier": 1.6,
        "reach_multiplier": 2,
        "attack_duration": 0.5,
        "hit_delay_pct": 0.5,
        "max_path_time": 1,
        "x_max_rotation": 90
      },
      "minecraft:behavior.random_stroll": { "priority": 7, "speed_multiplier": 1 },
      "minecraft:behavior.look_at_player": { "priority": 8, "look_distance": 6, "probability": 0.02 },
      "minecraft:behavior.random_look_around": { "priority": 9 },
      "minecraft:follow_range": { "value": 128 },
      "minecraft:dweller": {
        "dwelling_type": "village",
        "dweller_role": "hostile",
        "update_interval_base": 60,
        "update_interval_variant": 40,
        "can_find_poi": false,
        "can_migrate": true,
        "first_founding_reward": 0
      },
      "minecraft:behavior.move_to_village": { "priority": 4, "speed_multiplier": 1, "goal_radius": 2 },
      "minecraft:behavior.nearest_attackable_target": {
        "priority": 4,
        "must_see": true,
        "reselect_targets": false,
        "must_see_forget_duration": 0,
        "entity_types": [
          {
            "filters": {
              "all_of": [
                { "test": "is_visible", "subject": "other", "value": true },
                {
                  "any_of": [
                    { "test": "is_family", "subject": "other", "value": "player" },
                    { "test": "is_family", "subject": "other", "value": "snowgolem" },
                    { "test": "is_family", "subject": "other", "value": "irongolem" },
                    { "test": "is_family", "subject": "other", "value": "villager" },
                    { "test": "is_family", "subject": "other", "value": "wandering_trader" },
                    { "test": "is_family", "subject": "other", "value": "illager" },
                    { "test": "is_family", "subject": "other", "value": "turret" }
                  ]
                }
              ]
            },
            "max_dist": 96
          }
        ]
      },
      "minecraft:on_target_acquired": {
        "filters": { "test": "is_family", "subject": "other", "operator": "!=", "value": "turret" },
        "event": "rza:alert_others",
        "target": "self"
      },
      "minecraft:physics": {},
      "minecraft:pushable": { "is_pushable": true, "is_pushable_by_piston": true },
      "minecraft:conditional_bandwidth_optimization": {},
      "minecraft:ambient_sound_interval": { "event_name": "ambient", "range": 10, "value": 7 }
    }
  }
}
