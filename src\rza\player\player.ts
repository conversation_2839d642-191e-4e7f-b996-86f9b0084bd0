import { world } from "@minecraft/server";
import { cleanupFlamethrowerData } from "../weapons/flamethrower";
import { meleeWeaponCooldown } from "../weapons/melee";

/**
 * Handle player leave event to cleanup any active systems
 */
world.beforeEvents.playerLeave.subscribe((data) => {
  const player = data.player;
  // Cleanup flamethrower data
  cleanupFlamethrowerData(player.id);
  // Cleanup melee weapon cooldown
  meleeWeaponCooldown.delete(player.id);
  return;
});
